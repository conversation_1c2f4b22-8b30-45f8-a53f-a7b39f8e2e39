# Core FastAPI dependencies
fastapi>=0.110.0,<0.111.0
uvicorn>=0.29.0,<0.30.0

# Email and security
aiosmtplib==2.0.0
bcrypt==4.0.1
email-validator>=2.0.0,<3.0.0
cryptography>=3.4.8
PyJWT>=1.7.1

# HTTP and networking
httpcore>=0.16.3
httpx>=0.23.1
h11>=0.14.0
dnspython>=2.2.1
sniffio>=1.3.0
rfc3986>=1.5.0
idna>=3.4
websockets>=10.4

# CLI and environment handling
colorama>=0.4.4
awscli>=1.32.0
python-dotenv>=1.0.0,<2.0.0

# Serialization and data handling
pandas>=2.0.0,<2.3.0
orjson>=3.8.3
ujson>=5.6.0
PyYAML>=6.0.1
requests>=2.31.0
openpyxl
numpy>=1.26.0,<2.0.0
matplotlib>=3.5.0

# MongoDB and DB
pymongo>=3.10.1
sqlalchemy
psycopg2-binary
geopy>=2.2.0

# Jinja and templates
Jinja2>=3.1.3,<4.0.0

# LLM and AI tools
openai>=1.80.0,<2.0.0
llama-index>=0.12.0
llama-index-experimental>=0.5.0
llama-index-llms-openai>=0.4.0
agno>=1.5.0,<2.0.0
langchain>=0.1.1
langchain-community>=0.0.13
langchain-core>=0.1.13
langchain-openai>=0.0.3
langsmith>=0.0.83
# Background tasks
celery==5.3.0
redis==5.0.1

# PDF and reporting
reportlab==4.0.0

# JWT and authentication
fastapi-jwt-auth==0.5.0

# Google API
google-api-python-client
google-auth-oauthlib

# Server-Sent Events
sse-starlette

# Search API
google-search-results
