# RESTful API with Python, FastAPI, Pydantic, and MongoDB

## 1. API with Python, FastAPI, and MongoDB: JWT Authentication
- How to Setup FastAPI with MongoDB
- Starting the FastAPI Server
- Set up Environment Variables with Pydantic
- Connect to the MongoDB Database
- Creating the Schemas with Pydantic
- Create Serializers for the MongoDB BSON Documents
- Password Management in FastAPI
- Creating Utility Functions to Sign and Verify JWTs
- Creating the Authentication Controllers in FastAPI
    - User Registration Handler
    - User Sign-in Handler
    - Refresh Access Token Handler
    - Sign out User Handler
- How to Protect Private Routes
- Creating a User Handler
- Adding the API Routes and CORS
- Testing the API with Postman

## 2. Build API with Python & FastAPI: SignUp User and Verify Email

This article will teach you how to send HTML Emails with Python, FastAPI, PyMongo, MongoDB, Jinja2, and Docker. Also, you will learn how to use Jinja2 to generate different HTML templates.

### Topics Covered

- Send HTML Emails with Jinja2 & FastAPI Example
- Creating the SMTP Provider Account
- Edit the Environment Variables File
- Validating the Environment Variables with Pydantic
- Creating the HTML Email Templates in FastAPI
- Creating the SMTP Email Sender
- Sending the HTML Emails in FastAPI
- Update the SignUp Controller
- Create a Handler to Validate the Verification Code

## 3. CRUD RESTful API Server with Python, FastAPI, and MongoDB

This article will teach you how to create a CRUD RESTful API with Python, FastAPI, PyMongo, MongoDB, and Docker-compose to perform the basic Create/Read/Update/Delete operations against a database.

### Topics Covered

- Python, FastAPI, MongoDB CRUD API Overview
- Setting up FastAPI with MongoDB
    - Installing FastAPI
- Running the FastAPI Server
- Loading Environment Variables with Pydantic
- Connecting to the MongoDB Database Server
- Creating the Schemas with Pydantic
- Serializers for the MongoDB Documents
- Creating the API Route Controllers
    - Get All Posts Controller
    - Create New Post Controller
    - Update Post Controller
    - Get Single Post Controller
    - Delete Post Controller
- Add the Routes to the FastAPI Middleware Stack


### Docker Cmds
docker build -t jobv4 .
docker run -d -p 8000:8000 --name jobv4-container jobv4
