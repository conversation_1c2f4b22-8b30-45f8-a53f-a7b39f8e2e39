"""
Utility functions for generating MongoDB database documentation.
"""
import os
from typing import Dict, List, Any, Optional, Tuple
from pymongo import MongoClient
from pymongo.database import Database
from pymongo.collection import Collection
from bson import ObjectId
import json
from datetime import datetime
from app.models.llm import CollectionSchema, CollectionRelationship, CollectionGroup

# Maximum number of documents to sample for schema inference
MAX_SAMPLE_DOCS = 3

def get_mongodb_client() -> MongoClient:
    """Get MongoDB client from environment variables."""
    from app.config import settings
    return MongoClient(settings.DATABASE_URL)

def get_database(client: MongoClient, db_name: Optional[str] = None) -> Database:
    """Get MongoDB database."""
    from app.config import settings
    db_name = db_name or settings.MONGO_INITDB_DATABASE
    return client[db_name]

def extract_collection_names(db: Database) -> List[str]:
    """Extract all collection names from the database."""
    return db.list_collection_names()

def infer_field_type(value: Any) -> str:
    """Infer the type of a field value."""
    if value is None:
        return "null"
    elif isinstance(value, str):
        return "string"
    elif isinstance(value, bool):
        return "boolean"
    elif isinstance(value, int):
        return "integer"
    elif isinstance(value, float):
        return "float"
    elif isinstance(value, ObjectId):
        return "ObjectId"
    elif isinstance(value, datetime):
        return "date"
    elif isinstance(value, list):
        if value:
            # If the list has elements, infer the type of the first element
            element_type = infer_field_type(value[0])
            return f"array<{element_type}>"
        else:
            return "array"
    elif isinstance(value, dict):
        return "object"
    else:
        return str(type(value).__name__)

def infer_collection_schema(collection: Collection, include_sample_data: bool = False) -> CollectionSchema:
    """Infer the schema of a collection by sampling documents."""
    schema_fields = {}
    sample_data = []
    
    # Get a sample of documents
    cursor = collection.find().limit(MAX_SAMPLE_DOCS).sort([("_id", -1)])
    documents = list(cursor)
    
    # If no documents, return empty schema
    if not documents:
        return CollectionSchema(
            name=collection.name,
            fields={},
            sample_data=[],
            estimated_document_count=0
        )
    
    # Process each document to build the schema
    for doc in documents:
        if include_sample_data and len(sample_data) < 5:  # Limit to 5 sample documents
            # Convert ObjectId to string for JSON serialization
            doc_copy = json.loads(json.dumps(doc, default=str))
            sample_data.append(doc_copy)
        
        # Update schema with fields from this document
        for field_name, field_value in doc.items():
            field_type = infer_field_type(field_value)
            
            if field_name not in schema_fields:
                schema_fields[field_name] = {
                    "type": field_type,
                    "required": True,  # Assume required until we find a document without it
                    "example": str(field_value)[:100] if field_value is not None else None  # Truncate long examples
                }
            else:
                # If field exists in schema but with different type, mark as mixed
                if schema_fields[field_name]["type"] != field_type:
                    schema_fields[field_name]["type"] = f"mixed({schema_fields[field_name]['type']}, {field_type})"
    
    # Check which fields are not in all documents (not required)
    for field_name in schema_fields:
        field_count = sum(1 for doc in documents if field_name in doc)
        if field_count < len(documents):
            schema_fields[field_name]["required"] = False
    
    return CollectionSchema(
        name=collection.name,
        fields=schema_fields,
        sample_data=sample_data if include_sample_data else None,
        estimated_document_count=collection.estimated_document_count()
    )

def infer_relationships(collections: List[CollectionSchema]) -> List[CollectionRelationship]:
    """Infer relationships between collections based on field names."""
    relationships = []
    collection_names = [c.name for c in collections]
    
    for source_collection in collections:
        for field_name, field_info in source_collection.fields.items():
            # Check for direct references (field ends with Id or _id)
            if field_name.endswith("Id") or field_name.endswith("_id"):
                # Extract potential target collection name
                potential_target = field_name.replace("Id", "").replace("_id", "")
                
                # Check for plural form
                if potential_target.endswith("s"):
                    potential_target = potential_target[:-1]
                
                # Check if a matching collection exists
                for target_name in collection_names:
                    if target_name.lower() == potential_target.lower() or \
                       target_name.lower() == f"{potential_target.lower()}s":
                        relationships.append(CollectionRelationship(
                            source_collection=source_collection.name,
                            target_collection=target_name,
                            relationship_type="many-to-one",  # Assume many-to-one for _id references
                            source_field=field_name,
                            target_field="_id",
                            description=f"Inferred relationship from {source_collection.name}.{field_name} to {target_name}._id"
                        ))
            
            # Check for array references (field type is array and name suggests a collection)
            elif field_info["type"].startswith("array") and not field_name.endswith("s"):
                potential_target = field_name
                
                # Check for plural form
                if potential_target.endswith("s"):
                    potential_target = potential_target[:-1]
                
                # Check if a matching collection exists
                for target_name in collection_names:
                    if target_name.lower() == potential_target.lower() or \
                       target_name.lower() == f"{potential_target.lower()}s":
                        relationships.append(CollectionRelationship(
                            source_collection=source_collection.name,
                            target_collection=target_name,
                            relationship_type="one-to-many",  # Assume one-to-many for array references
                            source_field=field_name,
                            target_field="_id",
                            description=f"Inferred relationship from {source_collection.name}.{field_name} to {target_name}._id"
                        ))
    
    return relationships

def group_collections(collections: List[CollectionSchema], relationships: List[CollectionRelationship]) -> List[CollectionGroup]:
    """Group collections based on naming patterns and relationships."""
    groups = []
    grouped_collections = set()
    
    # Group by common prefixes
    prefixes = {}
    for collection in collections:
        name = collection.name
        
        # Extract prefix (before first underscore or capital letter after first)
        prefix = name.split('_')[0] if '_' in name else name
        for i, char in enumerate(name):
            if i > 0 and char.isupper():
                prefix = name[:i]
                break
        
        if prefix not in prefixes:
            prefixes[prefix] = []
        
        prefixes[prefix].append(name)
    
    # Create groups for prefixes with multiple collections
    for prefix, collection_names in prefixes.items():
        if len(collection_names) > 1:
            groups.append(CollectionGroup(
                name=f"{prefix.capitalize()} Group",
                collections=collection_names,
                description=f"Collections related to {prefix}"
            ))
            grouped_collections.update(collection_names)
    
    # Group by relationships
    relationship_groups = {}
    for rel in relationships:
        source = rel.source_collection
        target = rel.target_collection
        
        # Skip if both are already in a group
        if source in grouped_collections and target in grouped_collections:
            continue
        
        group_key = f"{source}_{target}"
        if group_key not in relationship_groups:
            relationship_groups[group_key] = set()
        
        relationship_groups[group_key].add(source)
        relationship_groups[group_key].add(target)
    
    # Create groups for relationships
    for group_key, collection_set in relationship_groups.items():
        if len(collection_set) > 1:
            collection_list = list(collection_set)
            # Get a common name for the group
            common_name = collection_list[0].split('_')[0] if '_' in collection_list[0] else collection_list[0]
            
            groups.append(CollectionGroup(
                name=f"{common_name.capitalize()} Related Collections",
                collections=collection_list,
                description=f"Collections related through references: {', '.join(collection_list)}"
            ))
            grouped_collections.update(collection_list)
    
    # Add ungrouped collections as individual groups
    for collection in collections:
        if collection.name not in grouped_collections:
            groups.append(CollectionGroup(
                name=f"{collection.name} Group",
                collections=[collection.name],
                description=f"Individual collection: {collection.name}"
            ))
    
    return groups

def get_database_documentation(include_sample_data: bool = False) -> Tuple[List[CollectionSchema], List[CollectionRelationship], List[CollectionGroup]]:
    """Get comprehensive documentation about the MongoDB database."""
    client = get_mongodb_client()
    db = get_database(client)
    
    # Get all collections
    collection_names = extract_collection_names(db)
    # Infer schema for each collection
    collections = [infer_collection_schema(db[name], include_sample_data) for name in collection_names]

    # Infer relationships between collections
    relationships = infer_relationships(collections)
    
    # Group collections
    groups = group_collections(collections, relationships)
    
    return collections, relationships, groups
