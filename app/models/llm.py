from pydantic import BaseModel, Field
from typing import List, Optional, Literal, Dict, Any
from datetime import datetime


class RecipeRequest(BaseModel):
    menu_master: dict
    menu_recipes: list

class StartProcessingRequest(BaseModel):
    tenantId: str

class ProcessingResponse(BaseModel):
    success: bool
    processingId: Optional[str] = None
    message: Optional[str] = None

class StatusResponse(BaseModel):
    status: str
    progress: int
    currentStep: int
    message: Optional[str] = None
    total_menus: Optional[int] = None
    processed_menus: Optional[int] = None
    estimated_time_remaining: Optional[int] = None


class ChatMessage(BaseModel):
    """Model for a single chat message."""
    id: Optional[int] = None
    content: str
    type: Literal["human", "ai"]
    created_at: datetime = Field(default_factory=datetime.now)


class ConversationHistoryResponse(BaseModel):
    """Model for the conversation history response."""
    tenant_id: str
    messages: List[ChatMessage]
    success: bool = True


class OutletDetails(BaseModel):
    outletName: str
    outletAddress: str
    outletWorkAreas: List[str]
    outletAbbreviation: Optional[str] = None

class TobaccoInfo(BaseModel):
    tobaccoService: Literal["Yes", "No"]

class BeverageInfo(BaseModel):
    alcoholService: Literal["Yes", "No"]

class SignatureElements(BaseModel):
    signatureDishes: List[str]

class CuisineMenuCount(BaseModel):
    cuisine: str
    menuItemCount: int

class RestaurantMenuPredictionData(BaseModel):
    totalOutlets: int
    outletDetails: List[OutletDetails]
    commonCuisinesAcrossOutlets: List[str]
    cuisineMenuCounts: Optional[List[CuisineMenuCount]] = None
    signatureElements: SignatureElements
    beverageInfo: BeverageInfo
    tobaccoInfo: TobaccoInfo


class DatabaseDocumentationRequest(BaseModel):
    """Request model for database documentation generation."""
    tenant_id: Optional[str] = None
    include_sample_data: bool = False
    output_format: Literal["json", "pdf"] = "json"


class CollectionSchema(BaseModel):
    """Model representing a MongoDB collection schema."""
    name: str
    fields: Dict[str, Any]
    sample_data: Optional[List[Dict[str, Any]]] = None
    estimated_document_count: Optional[int] = None


class CollectionRelationship(BaseModel):
    """Model representing a relationship between MongoDB collections."""
    source_collection: str
    target_collection: str
    relationship_type: str  # "one-to-one", "one-to-many", "many-to-many"
    source_field: str
    target_field: str
    description: str


class CollectionGroup(BaseModel):
    """Model representing a logical grouping of collections."""
    name: str
    collections: List[str]
    description: str
