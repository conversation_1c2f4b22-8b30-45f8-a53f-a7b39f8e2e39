import os
import time
from datetime import datetime
from typing import Dict, Any
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, as_completed

from fastapi import APIRouter, Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from dotenv import load_dotenv

from app.utility.report import grnStatusReport, store_variance, inventoryConsumptionNew
from app.utility.dashboard_agents import (
    generate_purchase_dashboard,
    generate_inventory_dashboard,
    generate_reconciliation_dashboard
)
from app.database import roloposconfigsCol, branchesCol
import requests

load_dotenv()

router = APIRouter()
security = HTTPBearer()

def _fetch_mappings_from_database(tenant_id: str) -> Dict[str, Any]:
    """Fetch department group category mappings and category workarea mappings from database"""
    try:
        config = roloposconfigsCol.find_one({"tenantId": tenant_id})

        if not config:
            print(f"DEBUG: No configuration found for tenant {tenant_id}")
            return {
                'department_group_category_mappings': [],
                'category_workarea_mappings': []
            }

        mapping_config = config.get("mappingConfig", {})

        # Get group-category mappings
        department_group_category_mappings = mapping_config.get("departmentGroupCategoryMappings", [])

        # Get category-workarea mappings (legacy format)
        category_workarea_mappings = mapping_config.get("categoryWorkareaMappings", [])

        # If no legacy category-workarea mappings, try to derive from group-workarea mappings
        if not category_workarea_mappings:
            department_group_workarea_mappings = mapping_config.get("departmentGroupWorkareaMappings", [])

            # Create a mapping from categories to workareas based on group mappings
            category_to_workareas = {}

            for group_category_mapping in department_group_category_mappings:
                group_id = group_category_mapping.get('groupId')
                categories = group_category_mapping.get('categories', [])

                # Find corresponding workarea mapping for this group
                for group_workarea_mapping in department_group_workarea_mappings:
                    if group_workarea_mapping.get('groupId') == group_id:
                        work_areas = group_workarea_mapping.get('workAreas', [])

                        # Map each category to these workareas
                        for category in categories:
                            if category not in category_to_workareas:
                                category_to_workareas[category] = []
                            category_to_workareas[category].extend(work_areas)

            # Convert to legacy format
            category_workarea_mappings = [
                {
                    'categoryName': category,
                    'workAreas': list(set(workareas))  # Remove duplicates
                }
                for category, workareas in category_to_workareas.items()
            ]

        print(f"DEBUG: Fetched mappings - Group-category: {len(department_group_category_mappings)}, Category-workarea: {len(category_workarea_mappings)}")

        # Get department_group_workarea_mappings for return
        department_group_workarea_mappings = mapping_config.get("departmentGroupWorkareaMappings", [])

        return {
            'department_group_category_mappings': department_group_category_mappings,
            'department_group_workarea_mappings': department_group_workarea_mappings,
            'category_workarea_mappings': category_workarea_mappings
        }

    except Exception as e:
        print(f"ERROR: Failed to fetch mappings from database: {str(e)}")
        return {
            'department_group_category_mappings': [],
            'category_workarea_mappings': []
        }

async def authenticate(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    if not credentials:
        raise HTTPException(status_code=401, detail="Not authenticated")
    expected_token = os.getenv("BEARER_TOKEN")
    if credentials.credentials != expected_token:
        raise HTTPException(status_code=401, detail="Invalid token")
    return credentials.credentials

# ===== CONFIGURATION =====
DASHBOARD_CONFIG = {
    "chart_colors": [
        '#ff8c42',  # Orange primary
        '#ffb366',  # Orange light
        '#87a96b',  # Sage green
        '#6b9bd2',  # Soft blue
        '#9b7bb8',  # Muted purple
        '#8d7b68',  # Warm gray
        '#d4a5a5',  # Dusty rose
        '#ffc999',  # Orange lighter
        '#a4c085',  # Sage green light
        '#85aedb',  # Soft blue light
        '#af95c6',  # Muted purple light
        '#a69082',  # Warm gray light
        '#ddb8b8',  # Dusty rose light
        '#ffe0cc',  # Orange lightest
        '#f4a261'   # Warning orange
    ],
    "chart_types": {
        "bar": "Bar Chart",
        "horizontalBar": "Horizontal Bar Chart",
        "line": "Line Chart",
        "doughnut": "Doughnut Chart",
        "pie": "Pie Chart",
        "radar": "Radar Chart",
        "polarArea": "Polar Area Chart"
    },
    "currency": {"code": "INR", "symbol": "₹"},
    "dashboard_types": [
        {"value": "inventory", "label": "Inventory Dashboard"},
        {"value": "purchase", "label": "Purchase Dashboard"},
        {"value": "reconciliation", "label": "COGS Dashboard"},

    ],
    "base_date_options": [
        {"value": "deliveryDate", "label": "GRN Date(System Entry Date)"},
        {"value": "invoiceDate", "label": "Vendor Invoice Date"},
        {"value": "grnDate", "label": "Goods Received Date"}
    ],
    "default_chart_options": {
        "responsive": True,
        "maintainAspectRatio": False,
        "plugins": {
            "legend": {
                "display": True,
                "position": "top",
                "labels": {
                    "usePointStyle": True,
                    "padding": 15,
                    "font": {"size": 11}
                }
            },
            "tooltip": {
                "backgroundColor": "rgba(255, 255, 255, 0.95)",
                "titleColor": "#333",
                "bodyColor": "#666",
                "borderColor": "#ffb366",
                "borderWidth": 2,
                "cornerRadius": 6
            }
        },
        "scales": {
            "x": {
                "grid": {"display": False},
                "ticks": {"font": {"size": 10}}
            },
            "y": {
                "beginAtZero": True,
                "grid": {"color": "#e9ecef"},
                "ticks": {"font": {"size": 10}}
            }
        }
    },
    "default_chart_options": {
        "responsive": True,
        "maintainAspectRatio": False,
        "plugins": {
            "legend": {
                "display": True,
                "position": "top",
                "labels": {
                    "usePointStyle": True,
                    "padding": 15,
                    "font": {"size": 11}
                }
            },
            "tooltip": {
                "backgroundColor": "rgba(255, 255, 255, 0.95)",
                "titleColor": "#333",
                "bodyColor": "#666",
                "borderColor": "#ffb366",
                "borderWidth": 2,
                "cornerRadius": 6
            }
        },
        "scales": {
            "x": {
                "grid": {"display": False},
                "ticks": {"font": {"size": 10}}
            },
            "y": {
                "beginAtZero": True,
                "grid": {"color": "#e9ecef"},
                "ticks": {"font": {"size": 10}}
            }
        }
    },
    "summary_card_config": {
        "colors": {
            "currency": "#ffb366",
            "number": "#ff9d4d",
            "percentage": "#ffc999",
            "text": "#6c757d"
        },
        "icons": {
            "currency": "account_balance_wallet",
            "number": "analytics",
            "percentage": "percent",
            "text": "info"
        }
    },
    "ui_config": {
        "default_date_range_days": 30,
        "default_dashboard_type": "inventory",
        "default_base_date": "deliveryDate"
    }
}

# ===== ROUTES =====
@router.get("/config")
async def get_dashboard_config(_: str = Depends(authenticate)) -> Dict[str, Any]:
    """Get global dashboard configuration for dynamic frontend rendering"""
    return {"status": "success", "data": DASHBOARD_CONFIG}




@router.get("/departments/{tenant_id}")
async def get_departments(tenant_id: str, _: str = Depends(authenticate)) -> Dict[str, Any]:
    config = roloposconfigsCol.find_one({"tenantId": tenant_id})
    if not config:
        return {"status": "error", "message": "Tenant configuration not found"}

    credentials = {"emailId": config.get('emailId'), "password": config.get('password')}
    rms_api_url = "https://rms-api.digitory.com"

    login_response = requests.post(f"{rms_api_url}/login", json=credentials,
                                 headers={"Content-Type": "application/json", "App-Id": "inventory"}, timeout=30)

    if login_response.status_code != 200:
        return {"status": "error", "message": "RMS API authentication failed"}

    auth_data = login_response.json()
    token = auth_data.get("loggedInEmployee").get("token")
    account_id = auth_data.get("loggedInEmployee").get("accountID")

    if not token or not account_id:
        return {"status": "error", "message": "Invalid RMS API authentication response"}

    departments_response = requests.get(f"{rms_api_url}/account/{account_id}/departments",
                                      headers={"Authorization": f"Bearer {token}", "Content-Type": "application/json"}, timeout=30)

    if departments_response.status_code != 200:
        return {"status": "error", "message": "Failed to fetch departments from RMS API"}

    departments_data = departments_response.json()
    departments = [{"id": dept.get("id"), "name": dept.get("name")} for dept in departments_data if isinstance(departments_data, list)]
    return {"status": "success", "data": departments}

def get_department_sales_data(tenant_id: str, store_id: str, from_date: str, to_date: str) -> Dict[str, Any]:
    try:
        config = roloposconfigsCol.find_one({"tenantId": tenant_id})
        if not config:
            return {"status": "error", "message": "Tenant configuration not found"}

        credentials = {"emailId": config.get('emailId'), "password": config.get('password')}
        rms_api_url = "https://rms-api.digitory.com"

        login_response = requests.post(f"{rms_api_url}/login", json=credentials,
                                     headers={"Content-Type": "application/json", "App-Id": "inventory"}, timeout=30)

        if login_response.status_code != 200:
            return {"status": "error", "message": "RMS API authentication failed"}

        auth_data = login_response.json()
        token = auth_data.get("loggedInEmployee").get("token")
        account_id = auth_data.get("loggedInEmployee").get("accountID")

        if not token or not account_id:
            return {"status": "error", "message": "Invalid RMS API authentication response"}

        sales_payload = {
            "storeID": int(store_id),
            "fromDate": from_date,
            "toDate": to_date,
            "columns": [{"text": "Department Summary", "sortable": False, "symbol": False,
                        "ordinal": 9, "mandatory": False, "selected": True, "key": "departmentSplitUp"}]
        }

        sales_response = requests.post(f"{rms_api_url}/api/v2/reports/accounts/{account_id}/saleRecapReport",
                                     json=sales_payload, headers={"Authorization": f"Bearer {token}",
                                     "Content-Type": "application/json", "App-Id": "inventory"}, timeout=30)

        if sales_response.status_code != 200:
            return {"status": "error", "message": "Failed to fetch sales data from RMS API"}

        return {"status": "success", "data": sales_response.json()}
    except Exception as e:
        return {"status": "error", "message": f"Error fetching sales data: {str(e)}"}


def _fetch_store_variance_data(job: Dict[str, Any]) -> pd.DataFrame:
    try:
        return store_variance(job)
    except Exception:
        return pd.DataFrame()

def _fetch_inventory_consumption_data(job: Dict[str, Any]) -> pd.DataFrame:
    try:
        return inventoryConsumptionNew(job)
    except Exception:
        return pd.DataFrame()

def _fetch_purchase_data(job: Dict[str, Any]) -> pd.DataFrame:
    try:
        return grnStatusReport(job)
    except Exception:
        return pd.DataFrame()

def _fetch_sales_data(tenant_id: str, store_id: str, start_date: str, end_date: str) -> Dict[str, Any]:
    try:
        return get_department_sales_data(tenant_id, store_id, start_date, end_date)
    except Exception as e:
        return {"status": "error", "message": f"Error fetching sales data: {str(e)}"}


@router.post("/smart_ask")
async def smart_ask(request: Dict[str, Any], _: str = Depends(authenticate)) -> Dict[str, Any]:
    try:
        filters = request.get('filters', {})
        tenant_id = request.get('tenant_id', '')
        dashboard_type = request.get('dashboard_type', 'purchase')

        department_group_category_mappings = []
        department_group_workarea_mappings = []
        category_workarea_mappings = []

        if dashboard_type == 'reconciliation':
            mappings = _fetch_mappings_from_database(tenant_id)
            department_group_category_mappings = mappings.get('department_group_category_mappings', [])
            department_group_workarea_mappings = mappings.get('department_group_workarea_mappings', [])
            category_workarea_mappings = mappings.get('category_workarea_mappings', [])

        job = _build_job_config(tenant_id, filters)
        dashboard_data = _generate_dashboard(job, dashboard_type, tenant_id, [], category_workarea_mappings, department_group_category_mappings, department_group_workarea_mappings)
        return {"status": "success", "data": dashboard_data}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid request data: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

def _build_job_config(tenant_id: str, filters: Dict[str, Any]) -> Dict[str, Any]:
    try:
        return {
            'tenantId': tenant_id,
            'details': {
                'selectedRestaurants': filters.get('locations', []),
                'selectedWorkAreas': filters.get('workAreas', []),
                'selectedBaseDate': filters.get('baseDate', 'deliveryDate'),
                'selectedCategories': filters.get('categories', []),
                'selectedSubCategories': filters.get('subcategories', []),
                'selectedDepartments': filters.get('departments', []),
                'selectedCategoryWorkAreas': filters.get('categoryWorkareaMappings', []),
                'startDate': datetime.strptime(filters.get('startDate'), '%Y-%m-%d'),
                'endDate': datetime.strptime(filters.get('endDate'), '%Y-%m-%d')
            }
        }
    except (ValueError, TypeError) as e:
        raise ValueError(f"Invalid date format in filters: {str(e)}")

def _generate_dashboard(job: Dict[str, Any], dashboard_type: str, tenant_id: str = '', department_category_mappings: list = None, category_workarea_mappings: list = None, department_group_category_mappings: list = None, department_group_workarea_mappings: list = None) -> Dict[str, Any]:
    if dashboard_type == 'purchase':
        job['details'].update({
            'selectedCategories': job['details'].get('selectedCategories', []) or ['all'],
            'selectedSubCategories': job['details'].get('selectedSubCategories', []) or ['all'],
            'selectedWorkAreas': job['details'].get('selectedWorkAreas', []) or ['all']
        })
        return generate_purchase_dashboard(grnStatusReport(job))

    elif dashboard_type == 'inventory':
        job['details'].update({
            'selectedCategories': job['details'].get('selectedCategories', []) or ['all'],
            'selectedSubCategories': job['details'].get('selectedSubCategories', []) or ['all'],
            'selectedVendors': [],
            # 'selectedWorkAreas': job['details'].get('selectedWorkAreas', []) or ['all'],
            'type': 'store_variance'
        })
        return generate_inventory_dashboard(store_variance(job))

    elif dashboard_type == 'reconciliation':
        all_workareas = []
        if department_group_workarea_mappings:
            for mapping in department_group_workarea_mappings:
                workareas = mapping.get('workAreas', [])
                all_workareas.extend(workareas)

        # Remove duplicates while preserving order
        unique_workareas = list(dict.fromkeys(all_workareas))

        # Use extracted workareas or fall back to frontend selection or 'all'
        selected_workareas = unique_workareas if unique_workareas else (job['details'].get('selectedWorkAreas', []) or ['all'])

        job['details'].update({
            'selectedCategories': job['details'].get('selectedCategories', []) or ['all'],
            'selectedSubCategories': job['details'].get('selectedSubCategories', []) or ['all'],
            'selectedVendors': [],
            'selectedWorkAreas': selected_workareas,
            'type': 'reconciliation'
        })

        sales_data = None
        selected_restaurants = job['details'].get('selectedRestaurants', [])
        if selected_restaurants and len(selected_restaurants) == 1:
            restaurant_id = selected_restaurants[0]
            branch = branchesCol.find_one({"restaurantIdOld": restaurant_id})
            if branch and 'storeId' in branch:
                store_id = str(branch['storeId'])
                start_date = job['details']['startDate'].strftime('%d-%b-%Y')
                end_date = job['details']['endDate'].strftime('%d-%b-%Y')
                sales_response = _fetch_sales_data(tenant_id, store_id, start_date, end_date)
                if sales_response.get('status') == 'success':
                    sales_data = sales_response.get('data')

        store_variance_df = _fetch_store_variance_data(job)
        inventory_consumption_df = _fetch_inventory_consumption_data(job)
        purchase_df = _fetch_purchase_data(job) 

        return generate_reconciliation_dashboard(
            store_variance_df,
            inventory_consumption_df,
            purchase_df=purchase_df,
            sales_data=sales_data,
            category_workarea_mappings=category_workarea_mappings,
            department_group_category_mappings=department_group_category_mappings,
            department_group_workarea_mappings=department_group_workarea_mappings
        )

    else:
        raise ValueError(f"Invalid dashboard type: {dashboard_type}")
