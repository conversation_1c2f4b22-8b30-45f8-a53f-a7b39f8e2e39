from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.database import mappingCol
import os
from bson import ObjectId
from fastapi import Query

router = APIRouter()
security = HTTPBearer()
async def authenticate(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if not credentials:
        raise HTTPException(status_code=401, detail="Not authenticated")
    expected_token = os.getenv("BEARER_TOKEN")
    if credentials.credentials != expected_token:
        raise HTTPException(status_code=401, detail="Invalid token")
    return credentials.credentials


@router.get('/List')
def List(tenantId: str, export: bool ,itemCode: str,restaurantId: str,page: int = Query(1, ge=1), per_page: int = Query(10, ge=1), token: str = Depends(authenticate)):
    if export == True:
        mappingList = list(mappingCol.find({"tenantId": tenantId}).sort([("_id", -1)]))
    else :
        mappingList = list(mappingCol.find({"tenantId": tenantId,"itemCode":itemCode,"restaurantId": restaurantId}).sort([("_id", -1)]))
    count = 0
    for doc in mappingList:
        doc['_id'] = str(doc['_id'])
    return {"status": True, "message": "Listed Successfully", "data": mappingList,"count" : count}

@router.get('/{id}')
def Get(id: str, token: str = Depends(authenticate)):
    projection = {"_id": 0}
    menuMapping = list(mappingCol.find({"_id": ObjectId(id)},projection))
    return {"status": True, "message": "Data Retrieved Successfully", "data": menuMapping}

@router.post('/Create')
def Create(obj: dict, token: str = Depends(authenticate)):
    mappingCol.delete_many({'tenantId':obj['tenantId'],'itemCode': obj['itemCode'],'restaurantId': obj['restaurantId'] })
    mappingCol.insert_many(obj['mappings'])
    return {"status" : True,"message":"Added Successfully"}

@router.post('/Import')
def Import(obj: dict, token: str = Depends(authenticate)):
    data = obj['data']
    tenantId = obj['tenantId']
    mappingCol.delete_many({'tenantId':tenantId })
    mappingCol.insert_many(data)
    return {"status" : True,"message":"Imported Successfully!"}

@router.post('/{id}')
def Update(obj: dict, token: str = Depends(authenticate)):
    entry = {
        # 'tenantId' : tenantId,
        'restaurantId' : obj['restaurantId'],
        'storeId' : obj['storeId'],
        # 'itemCode' : itemCode.upper(),
        'floorNo' : obj['floorNo'],
        'section' : obj['section'],
        'workArea' : obj['workArea']
    }
    mappingCol.update_one({"_id": ObjectId(obj['id'])}, {'$set': entry})
    return {"status" : True,"message":"Updated Successfully"}