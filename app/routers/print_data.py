from fastapi import <PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.database import mappingCol
import os
from bson import ObjectId
from fastapi import Query
from pydantic import BaseModel
from io import BytesIO
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.units import  mm, cm
from reportlab.lib.utils import ImageReader
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.utils import ImageReader
from io import BytesIO
import base64

router = APIRouter()
security = HTTPBearer()
async def authenticate(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if not credentials:
        raise HTTPException(status_code=401, detail="Not authenticated")
    expected_token = os.getenv("BEARER_TOKEN")
    if credentials.credentials != expected_token:
        raise HTTPException(status_code=401, detail="Invalid token")
    return credentials.credentials

class Recipe(BaseModel):
    factory_name: str
    logo: str
    recipe_details: dict
    table_headers: list
    table_data: list
    summary: dict
    remarks: str = None
    footer: str = None

def recipe_pdf(data: Recipe):
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=10, leftMargin=10, topMargin=10, bottomMargin=20)
    elements = []

    styles = getSampleStyleSheet()
    styles.add(ParagraphStyle(name='CustomNormal', fontSize=8, leading=10))
    styles.add(ParagraphStyle(name='CustomBold', fontSize=12, leading=10, fontName='Helvetica-Bold'))
    styles.add(ParagraphStyle(name='LeftAlign', fontSize=8, leading=10, alignment=0))
    title = Paragraph(f"<b>{data.factory_name}</b>", styles['CustomBold'])
    
    elements.append(title)
    elements.append(Spacer(1, 8))
    # Decode the base64 logo and resize it to 3X3 cm
    base = data.logo
    if base.startswith("data:image/png;base64,"):
        base = base.replace("data:image/png;base64,", "")
    logo_data = base64.b64decode(base)
    logo = Image(BytesIO(logo_data), width=2.5*cm, height=2.5*cm)
    logo.hAlign = 'RIGHT'

    # Recipe details formatted as a list of Paragraphs
    recipe_details_data = []
    if data.recipe_details:
        recipe_details_data = [
            [Paragraph(f"<b>{key}</b>", styles['CustomNormal']), Paragraph(f"{value.upper()}", styles['CustomNormal'])]
            for key, value in data.recipe_details.items()
        ]

    # Create a table with 2 columns: recipe details on the left, logo on the right
    combined_data = Table([
        [Table(recipe_details_data, colWidths=[50*mm, None]), logo]
    ], colWidths=[A4[0] - 4*cm, 3*cm])  # Adjusting width to balance between text and logo
    combined_data.setStyle(TableStyle([
        ('ALIGN', (0, 0), (0, 0), 'LEFT'),
        ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
        ('VALIGN', (0, 0), (1, 0), 'TOP'),
        ('LEFTPADDING', (0, 0), (-1, -1), 0),
        ('RIGHTPADDING', (0, 0), (-1, -1), 0),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 0),
        ('TOPPADDING', (0, 0), (-1, -1), 0),
    ]))

    elements.append(combined_data)

    if data.table_headers and data.table_data:
        elements.append(Spacer(1, 12))
        col_count = len(data.table_headers)
        page_width = A4[0] - doc.leftMargin - doc.rightMargin
        col_widths = [page_width / col_count] * col_count
        table_data = [data.table_headers] + data.table_data

        wrapped_table_data = [
            [Paragraph(cell, styles['CustomNormal']) if isinstance(cell, str) else cell for cell in row]
            for row in table_data
        ]

        table = Table(wrapped_table_data, colWidths=col_widths)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor("#e0e0e0")),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 4),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 0.25, colors.grey),
            ('LEFTPADDING', (0, 0), (-1, -1), 4),
            ('RIGHTPADDING', (0, 0), (-1, -1), 4),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        elements.append(table)

    if data.remarks:
        elements.append(Spacer(1, 8))
        elements.append(Paragraph(f"<b>Remarks</b> {data.remarks}", styles['CustomNormal']))

    if data.summary:
        elements.append(Spacer(1, 12))
        summary_data = [[Paragraph(f"<b>{key}</b>", styles['CustomNormal']), Paragraph(f"{value}", styles['LeftAlign'])]
                        for key, value in data.summary.items()]

        summary_table = Table(summary_data, colWidths=[60*mm, 30*mm], hAlign='LEFT')
        summary_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 2),
            ('TOPPADDING', (0, 0), (-1, -1), 2),
            ('BACKGROUND', (0, 0), (-1, -1), colors.white),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 0),
            ('INNERGRID', (0, 0), (-1, -1), 0, colors.white),
            ('BOX', (0, 0), (-1, -1), 0, colors.white),
        ]))

        elements.append(summary_table)

    if data.footer:
        elements.append(Spacer(1, 15))
        elements.append(Paragraph(data.footer, styles['CustomNormal']))

    doc.build(elements)
    pdf_value = buffer.getvalue()
    buffer.close()
    pdf_base64 = base64.b64encode(pdf_value).decode('utf-8')
    return pdf_base64
   
@router.post("/generate-invoice")
def generate_invoice(data: Recipe):
    pdf_base64 = recipe_pdf(data)
    return {"pdf_base64": pdf_base64}