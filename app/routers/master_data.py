from fastapi import APIRouter, Depends, FastAPI, HTTPException , Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi import FastAP<PERSON>, BackgroundTasks
from pydantic import BaseModel
from app.database import tempMasterDataCol,autoIncCol,masterdataupdateconfigsCol,users,Stockvalues, roloposconfigsCol
import pandas as pd
import os
from datetime import datetime
from app.config import inventory_sheets,user_sheets,recipe_sheets
from app.classes import menu,subrecipe
import numpy as np
import bcrypt
from typing import Dict, List, Optional

app = FastAPI()
router = APIRouter()
salt=bcrypt.gensalt()

router = APIRouter()
security = HTTPBearer()
async def authenticate(credentials: HTTPAuthorizationCredentials = Depends(security)):
	if not credentials:
		raise HTTPException(status_code=401, detail="Not authenticated")
	expected_token = os.getenv("BEARER_TOKEN")
	if credentials.credentials != expected_token:
		raise HTTPException(status_code=401, detail="Invalid token")
	return credentials.credentials


@router.post('/updateData')
def updateData(params: dict, background_tasks: BackgroundTasks):    
	res = {} 
	config_rec = list(masterdataupdateconfigsCol.find({'type': 'masterdataUpdate'}))
	if config_rec:
		data = next((d for d in config_rec[0]['clients'] if d['tenantId'] == params['tenantId']), None)
	
	obj = {"tenantId": params['tenantId'], "type": params['type'], "active": True}
	temp_record = list(tempMasterDataCol.find(obj).sort([("_id", -1)]))
	session_id = ''
	
	if temp_record:
		temp_record[0]['modTs'] = datetime.now().strftime('%Y%m%d%H%M%S')
		temp_record[0]['excelUpload'] = params.get('excelUpload', False)
		tempMasterDataCol.update_one(obj, {'$set': temp_record[0]})
		session_id = temp_record[0]['sessionId']
	else:
		check_auto = list(autoIncCol.find({'_id': 'SEC'}))
		session_id = check_auto[0]['c'] + 1
		excel_upload = params.get('excelUpload', False)
		obj = {
			'tenantId': params['tenantId'],
			'userEmail': params['userEmail'],
			'type': params['type'],
			'active': True,
			'sessionId': session_id,
			'createTs': datetime.now().strftime('%Y%m%d%H%M%S'),
			'modTs': datetime.now().strftime('%Y%m%d%H%M%S'),
			'excelUpload': excel_upload,
			'newItem': True,
		}
		tempMasterDataCol.insert_one(obj)
		autoIncCol.update_one({'_id': 'SEC'}, {'$set': {'c': session_id}})
	md_session_directory = f"../digitoryjobsv4/masterdata/session/{data['full']}/{session_id}/"
	md_base_directory = f"../digitoryjobsv4/masterdata/base/{data['full']}/"
	if params.get('type') != 'recipe':
		os.makedirs(md_session_directory, exist_ok=True)
  
	############### To check if any missing inventory from stockvalues ###############
	if params.get('type') == 'inventory':
		inv_sheet = "inventory master"
		inv_session_file = f"{md_session_directory}{inv_sheet}.csv"
		inv_base_file = f"{md_base_directory}{inv_sheet}.csv"
		inv_session_df = pd.read_csv(inv_session_file) if os.path.exists(inv_session_file) else pd.read_csv(inv_base_file)
		session_itemcodes = inv_session_df['itemCode'].unique().tolist()
		db_itemcodes = Stockvalues.distinct("itemCode",{'tenantId' : params['tenantId'] , "ItemType":"Inventory"})
		# Identify missing item codes
		missing_itemcodes = set(db_itemcodes) - set(session_itemcodes)
		if missing_itemcodes:
			tempMasterDataCol.update_one({'sessionId': session_id},{"$set":{'active': False}})
			print("Missing item codes:", missing_itemcodes)
			res['message'] = "Your session is out of sync due to parallel updates. Please refresh the page to ensure you have the latest data."
			res['session'] = session_id
			res['out_of_sync'] = True
			return res

	for sheet, value in params['data'].items():
		session_file = f"{md_session_directory}{sheet}.csv"
		base_file = f"{md_base_directory}{sheet}.csv"
		session_df = pd.DataFrame()  
		if params['type'] == "recipe":
			session_df = pd.read_csv(base_file)
			session_df['portion'] = pd.to_numeric(session_df.get('portion', 1), errors='coerce')
			if sheet == "menu master":
				session_df['usedAtOutletDiscontinued'] = pd.to_numeric(session_df.get('usedAtOutletDiscontinued', 1), errors='coerce')
				session_df['usedWorkAreaDiscontinued'] = pd.to_numeric(session_df.get('usedWorkAreaDiscontinued', 1), errors='coerce')
				session_df['preparedAtDiscontinued'] = pd.to_numeric(session_df.get('preparedAtDiscontinued', 1), errors='coerce')
				session_df['parties'] = pd.to_numeric(session_df.get('parties', 1), errors='coerce')
			elif sheet == "Subrecipe Master":
				session_df['preparedAtDiscontinued'] = pd.to_numeric(session_df.get('preparedAtDiscontinued', 1), errors='coerce')
				session_df['usedAtOutletDiscontinued'] = pd.to_numeric(session_df.get('usedAtOutletDiscontinued', 1), errors='coerce')
				session_df['usedInWorkAreaDiscontinued'] = pd.to_numeric(session_df.get('usedInWorkAreaDiscontinued', 1), errors='coerce')
		else:
			session_df = pd.read_csv(session_file) if os.path.exists(session_file) else pd.read_csv(base_file)
			if sheet == "branches":
				session_df = session_df.assign(billTo=lambda x: x.get('billTo', '-'))
				session_df = session_df.assign(shipTo=lambda x: x.get('shipTo', '-'))
				session_df = session_df.assign(gstNo=lambda x: x.get('gstNo', ''))
				session_df = session_df.assign(panNo=lambda x: x.get('panNo', ''))
				session_df = session_df.assign(storeAvailable=lambda x: x.get('storeAvailable', '-'))
			elif sheet == "inventory master":
				session_df = session_df.assign(HSN_SAC=lambda x: x.get('HSN_SAC', '-'))
				session_df = session_df.assign(Ledger=lambda x: x.get('Ledger', '-'))
				session_df = session_df.assign(issuedToDiscontinued=lambda x: x.get('issuedToDiscontinued', '-'))
				session_df = session_df.assign(procuredAtDiscontinued=lambda x: x.get('procuredAtDiscontinued', '-'))
			elif sheet == "packagingmasters":
				session_df = session_df.assign(ParLevel=lambda x: x.get('ParLevel', 0))
           
		new_data = pd.DataFrame(value)
		for _idx, row in new_data.iterrows():
			row_uuid = row.get('row_uuid', None)
			row_uuid = None if row_uuid == '' else int(row_uuid)
			if row_uuid is not None and row_uuid in session_df['row_uuid'].values:
				row_to_update = session_df[session_df['row_uuid'] == row_uuid].index[0]
				if 'delete' in row.keys() and row['delete'] == True:
					session_df.drop(row_to_update, inplace=True)
				else :
					session_df.loc[row_to_update] = row
			else:
				max_row_uuid = pd.to_numeric(session_df['row_uuid']).max() if not session_df.empty else 0
				max_row_uuid += 1
				row['row_uuid'] = max_row_uuid
				session_df = session_df._append(row, ignore_index=True)

		session_df = session_df.apply(pd.to_numeric, errors='ignore')
		if params.get('type') == 'recipe':
			session_df.to_csv(base_file, index=False)
			if sheet == "menu master":
				background_tasks.add_task(menu.menuRecipe, params['tenantId'], data['full'], str(session_id))
			elif sheet == "Subrecipe Master":
				menu_item_codes = session_df.loc[session_df['modified'] == 'yes', 'menuItemCode'].tolist()
				background_tasks.add_task(subrecipe.subRecipe, params['tenantId'], data['full'], str(session_id),menu_item_codes)
			tempMasterDataCol.update_one({"sessionId": session_id}, {'$set': {'active': False}})
			session_df = session_df.drop(columns=['modified']) 	
			session_df['modified'] = '-'
			session_df['row_uuid'] = session_df.reset_index().index + 1
			session_df.to_csv(base_file, index=False)
		else:
			session_df.to_csv(session_file, index=False)
	res['message'] = 'updated the preview'
	res['session'] = session_id
	res['success'] = True
	return res

	
@router.get('/getData')
def getData(tenantId: str, category: str, specific: str = "NAN", itemCode: str = ""):
	res = {'success': False, 'sessionId': 0, 'data': []}
	file_path = ''
	config_rec = list(masterdataupdateconfigsCol.find({'type': 'masterdataUpdate'}))
	if config_rec:
		data = next((d for d in config_rec[0]['clients'] if d['tenantId'] == tenantId), None)
		md_base_directory = f"../digitoryjobsv4/masterdata/base/{data['full']}/"
		md_session_directory = ""
		obj = {"tenantId": tenantId, "type": category, "active": True}
		temp_record = list(tempMasterDataCol.find(obj).sort([("_id", -1)]))
		session_id = 0
		if temp_record:
			session_id = temp_record[0]['sessionId']
			md_session_directory = f"../digitoryjobsv4/masterdata/session/{data['full']}/{session_id}/"
		
		base = {}
		sheets = []
		if specific != "NAN":
			if category == 'inventory':
				sheets = [specific, 'packagingmasters'] if specific != "vendors" else [specific]
			elif category == 'user':
				sheets = [specific]
			else:
				sheets = [specific]
		else:
			if category == 'inventory':
				sheets = inventory_sheets
			elif category == 'user':
				sheets = user_sheets
			else:
				sheets = recipe_sheets

		for sheet in sheets:
			if md_session_directory:
				file_path = os.path.join(md_session_directory, f"{sheet}.csv")
				if not os.path.exists(file_path):
					file_path = os.path.join(md_base_directory, f"{sheet}.csv")
			else:
				file_path = os.path.join(md_base_directory, f"{sheet}.csv")
			if os.path.exists(file_path):
				df = pd.read_csv(file_path, dtype=str)
				df = df.apply(pd.to_numeric, errors='ignore')                
				df.replace([np.inf, -np.inf, np.nan], None, inplace=True)
				if sheet == 'inventory master' and specific != 'NAN':
					df = df[df['modified'].str.lower() == 'yes']
				if specific == "packagingmasters":
					df = df[df['InventoryCode'] == itemCode]
				elif specific == "Subrecipe Recipe":
					df = df[df['subRecipeCode'] == itemCode]
				elif specific == "menu recipes":
					df = df[df['menuItemCode'] == itemCode]
				base[sheet] = df.to_dict(orient='records')
		res['success'] = True
		res['data'] = base
		res['sessionId'] = session_id
	return res

@router.put('/reset_password')
async def reset_password(email: str, old_password: str, new_password: str, tenant_id: str, request: Request):

	user = users.find_one({'email': email, 'tenantId': tenant_id})
	
	if user is None:
		raise HTTPException(status_code=404, detail='User not found')
	
	existing_password_hash = user.get('password_digest')
	
	if not bcrypt.checkpw(old_password.encode('utf-8'), existing_password_hash.encode('utf-8')):
		raise HTTPException(status_code=403, detail='Incorrect password')
	
 
	hashed_password = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
	
	update_result = users.update_one({'email': email, 'tenantId': tenant_id}, {"$set": {'password_digest': hashed_password}})
	
	if update_result.modified_count == 1:
		return {'status': 'success', 'message': "Your password has been successfully updated."}
	else:
		raise HTTPException(status_code=500, detail='Failed to update password in the database')


class ClosingDetails(BaseModel):
	status: bool
	selectedClosingDates: Dict[str, str]  

class UpdateRequest(BaseModel):
	location: str
	closingDetails: Dict[str, ClosingDetails]

def convert_dates(details_dict):
	"""Convert date strings in selectedClosingDates to datetime objects if status is True."""
	if not details_dict['status']:
		details_dict['selectedClosingDates'] = {}
	else:
		if 'selectedClosingDates' in details_dict:
			for key, value in details_dict['selectedClosingDates'].items():
				try:
					details_dict['selectedClosingDates'][key] = datetime.strptime(value, "%Y-%m-%d").isoformat()
				except ValueError:
					continue
	return details_dict

@router.post("/update-closing-dates")
async def update_closing_dates(request: UpdateRequest):
	location = request.location
	closing_details = request.closingDetails

	for key, details in closing_details.items():
		tenant_id = key.split('@')[0]
		
		converted_details = convert_dates(details.dict())
		
		filter_query = {"tenantId": tenant_id}

		update_query = {
			"$set": {
				f"permission.closingDetails.{location}": converted_details
			}
		}

		result = roloposconfigsCol.update_one(filter_query, update_query)

		if result.matched_count == 0:
			raise HTTPException(status_code=404, detail="Tenant not found")

	return {"message": "Closing dates updated successfully"}


# ===== DEPARTMENT-CATEGORY-WORKAREA MAPPING MODELS =====

class DepartmentGroup(BaseModel):
	id: str
	name: str
	description: Optional[str] = None
	departmentIds: List[str]
	isActive: Optional[bool] = True

class DepartmentCategoryMapping(BaseModel):
	departmentId: str
	departmentName: str
	categories: List[str]

class DepartmentGroupCategoryMapping(BaseModel):
	groupId: str
	groupName: str
	categories: List[str]

class DepartmentGroupWorkareaMapping(BaseModel):
	groupId: str
	groupName: str
	workAreas: List[str]

class CategoryWorkareaMapping(BaseModel):
	categoryName: str
	workAreas: List[str]

# Simplified group-centric mapping request
class GroupCentricMappingRequest(BaseModel):
	tenantId: str
	groupMappings: List[dict]  # [{groupId, groupName, categories, workAreas}]

class MappingConfigRequest(BaseModel):
	tenantId: str
	departmentGroups: Optional[List[DepartmentGroup]] = []
	departmentCategoryMappings: List[DepartmentCategoryMapping]
	departmentGroupCategoryMappings: Optional[List[DepartmentGroupCategoryMapping]] = []
	departmentGroupWorkareaMappings: Optional[List[DepartmentGroupWorkareaMapping]] = []
	categoryWorkareaMappings: List[CategoryWorkareaMapping]

class MappingConfigResponse(BaseModel):
	success: bool
	message: str
	data: Optional[dict] = None


# ===== DEPARTMENT-CATEGORY-WORKAREA MAPPING ENDPOINTS =====

@router.post("/save-group-mappings")
async def save_group_mappings(request: GroupCentricMappingRequest, token: str = Depends(authenticate)):
	"""
	Save department group mappings in a group-centric format
	"""
	try:
		tenant_id = request.tenantId
		group_mappings = request.groupMappings

		# Validate input
		if not tenant_id:
			raise HTTPException(status_code=400, detail="Tenant ID is required")

		if not group_mappings:
			raise HTTPException(status_code=400, detail="Group mappings are required")

		print(f"[GROUP-CENTRIC API] Saving group mappings for tenant {tenant_id}")
		print(f"[GROUP-CENTRIC API] Received {len(group_mappings)} group mappings")
		for i, mapping in enumerate(group_mappings):
			print(f"[GROUP-CENTRIC API] Group {i+1}: {mapping.get('groupName')} - {len(mapping.get('categories', []))} categories, {len(mapping.get('workAreas', []))} work areas")

		# Process the group-centric mappings
		department_group_category_mappings = []
		department_group_workarea_mappings = []

		for mapping in group_mappings:
			group_id = mapping.get('groupId')
			group_name = mapping.get('groupName')
			categories = mapping.get('categories', [])
			work_areas = mapping.get('workAreas', [])

			if group_id and categories:
				# Add group-category mapping
				department_group_category_mappings.append({
					"groupId": str(group_id),
					"groupName": group_name,
					"categories": categories
				})

			if group_id and work_areas:
				# Add group-workarea mapping (group-level, not category-level)
				department_group_workarea_mappings.append({
					"groupId": str(group_id),
					"groupName": group_name,
					"workAreas": work_areas
				})

		print(f"[GROUP-CENTRIC API] Processed into {len(department_group_category_mappings)} group-category mappings")
		print(f"[GROUP-CENTRIC API] Processed into {len(department_group_workarea_mappings)} group-workarea mappings")

		# Get existing config to preserve department groups and other data
		config = roloposconfigsCol.find_one({"tenantId": tenant_id})
		existing_mapping_config = config.get("mappingConfig", {}) if config else {}

		# Update with group-centric structure (remove category-workarea mappings)
		mapping_data = {
			"departmentGroups": existing_mapping_config.get("departmentGroups", []),
			"departmentGroupCategoryMappings": department_group_category_mappings,
			"departmentGroupWorkareaMappings": department_group_workarea_mappings,
			"lastUpdated": datetime.now()
		}

		# Update roloposconfig document
		filter_query = {"tenantId": tenant_id}
		update_query = {"$set": {"mappingConfig": mapping_data}}

		result = roloposconfigsCol.update_one(filter_query, update_query)

		if result.matched_count == 0:
			raise HTTPException(status_code=404, detail="Tenant configuration not found")

		return MappingConfigResponse(
			success=True,
			message="Group mappings saved successfully",
			data={
				"groupCategoryMappingsCount": len(department_group_category_mappings),
				"groupWorkareaMappingsCount": len(department_group_workarea_mappings)
			}
		)

	except Exception as e:
		raise HTTPException(status_code=500, detail=f"Failed to save group mappings: {str(e)}")

@router.post("/save-mapping-config")
async def save_mapping_config(request: MappingConfigRequest, token: str = Depends(authenticate)):
	"""
	Save department-category-workarea mappings to roloposconfig collection
	"""
	try:
		tenant_id = request.tenantId

		# Prepare mapping data structure
		mapping_data = {
			"departmentGroups": [group.dict() for group in request.departmentGroups] if request.departmentGroups else [],
			"departmentCategoryMappings": [mapping.dict() for mapping in request.departmentCategoryMappings],
			"departmentGroupCategoryMappings": [mapping.dict() for mapping in request.departmentGroupCategoryMappings] if request.departmentGroupCategoryMappings else [],
			"categoryWorkareaMappings": [mapping.dict() for mapping in request.categoryWorkareaMappings],
			"lastUpdated": datetime.now()
		}

		# Update roloposconfig document
		filter_query = {"tenantId": tenant_id}
		update_query = {
			"$set": {
				"mappingConfig": mapping_data
			}
		}

		result = roloposconfigsCol.update_one(filter_query, update_query)

		if result.matched_count == 0:
			raise HTTPException(status_code=404, detail="Tenant configuration not found")

		return MappingConfigResponse(
			success=True,
			message="Mapping configuration saved successfully",
			data=mapping_data
		)

	except Exception as e:
		raise HTTPException(status_code=500, detail=f"Failed to save mapping configuration: {str(e)}")


@router.get("/get-mapping-config/{tenant_id}")
async def get_mapping_config(tenant_id: str, token: str = Depends(authenticate)):
	"""
	Get department-category-workarea mappings for a tenant
	"""
	try:
		# Find tenant configuration
		config = roloposconfigsCol.find_one({"tenantId": tenant_id})

		if not config:
			raise HTTPException(status_code=404, detail="Tenant configuration not found")

		# Get mapping configuration or return empty structure
		mapping_config = config.get("mappingConfig", {
			"departmentGroups": [],
			"departmentCategoryMappings": [],
			"departmentGroupCategoryMappings": [],
			"departmentGroupWorkareaMappings": [],
			"categoryWorkareaMappings": [],
			"lastUpdated": None
		})

		return MappingConfigResponse(
			success=True,
			message="Mapping configuration retrieved successfully",
			data=mapping_config
		)

	except HTTPException:
		raise
	except Exception as e:
		raise HTTPException(status_code=500, detail=f"Failed to retrieve mapping configuration: {str(e)}")


@router.delete("/clear-mapping-config/{tenant_id}")
async def clear_mapping_config(tenant_id: str, token: str = Depends(authenticate)):
	"""
	Clear department-category-workarea mappings for a tenant
	"""
	try:
		# Update roloposconfig document to remove mapping config
		filter_query = {"tenantId": tenant_id}
		update_query = {
			"$unset": {
				"mappingConfig": ""
			}
		}

		result = roloposconfigsCol.update_one(filter_query, update_query)

		if result.matched_count == 0:
			raise HTTPException(status_code=404, detail="Tenant configuration not found")

		return MappingConfigResponse(
			success=True,
			message="Mapping configuration cleared successfully"
		)

	except Exception as e:
		raise HTTPException(status_code=500, detail=f"Failed to clear mapping configuration: {str(e)}")


# ===== DEPARTMENT GROUP MANAGEMENT ENDPOINTS =====

class DepartmentGroupRequest(BaseModel):
	tenantId: str
	departmentGroups: List[DepartmentGroup]

@router.post("/save-department-groups")
async def save_department_groups(request: DepartmentGroupRequest, token: str = Depends(authenticate)):
	"""
	Save department groups for a tenant
	"""
	try:
		tenant_id = request.tenantId

		# Get existing mapping config
		config = roloposconfigsCol.find_one({"tenantId": tenant_id})
		if not config:
			raise HTTPException(status_code=404, detail="Tenant configuration not found")

		existing_mapping_config = config.get("mappingConfig", {})

		# Update department groups while preserving other mappings
		existing_mapping_config["departmentGroups"] = [group.dict() for group in request.departmentGroups]
		existing_mapping_config["lastUpdated"] = datetime.now()

		# Update roloposconfig document
		filter_query = {"tenantId": tenant_id}
		update_query = {
			"$set": {
				"mappingConfig": existing_mapping_config
			}
		}

		result = roloposconfigsCol.update_one(filter_query, update_query)

		if result.matched_count == 0:
			raise HTTPException(status_code=404, detail="Tenant configuration not found")

		return MappingConfigResponse(
			success=True,
			message="Department groups saved successfully"
		)

	except HTTPException:
		raise
	except Exception as e:
		raise HTTPException(status_code=500, detail=f"Failed to save department groups: {str(e)}")

@router.get("/get-department-groups/{tenant_id}")
async def get_department_groups(tenant_id: str, token: str = Depends(authenticate)):
	"""
	Get department groups for a tenant
	"""
	try:
		# Find tenant configuration
		config = roloposconfigsCol.find_one({"tenantId": tenant_id})

		if not config:
			raise HTTPException(status_code=404, detail="Tenant configuration not found")

		# Get department groups from mapping configuration
		mapping_config = config.get("mappingConfig", {})
		department_groups = mapping_config.get("departmentGroups", [])

		return MappingConfigResponse(
			success=True,
			message="Department groups retrieved successfully",
			data={"departmentGroups": department_groups}
		)

	except HTTPException:
		raise
	except Exception as e:
		raise HTTPException(status_code=500, detail=f"Failed to retrieve department groups: {str(e)}")

@router.post("/create-department-group")
async def create_department_group(request: dict, token: str = Depends(authenticate)):
	"""
	Create a new department group
	"""
	try:
		tenant_id = request.get("tenantId")
		group_name = request.get("groupName")
		group_description = request.get("groupDescription", "")
		department_ids = request.get("departmentIds", [])

		if not tenant_id or not group_name:
			raise HTTPException(status_code=400, detail="tenantId and groupName are required")

		# Get existing mapping config
		config = roloposconfigsCol.find_one({"tenantId": tenant_id})
		if not config:
			raise HTTPException(status_code=404, detail="Tenant configuration not found")

		existing_mapping_config = config.get("mappingConfig", {})
		existing_groups = existing_mapping_config.get("departmentGroups", [])

		# Generate new group ID
		import uuid
		new_group_id = str(uuid.uuid4())

		# Create new group
		new_group = {
			"id": new_group_id,
			"name": group_name,
			"description": group_description,
			"departmentIds": department_ids,
			"isActive": True
		}

		# Add to existing groups
		existing_groups.append(new_group)
		existing_mapping_config["departmentGroups"] = existing_groups
		existing_mapping_config["lastUpdated"] = datetime.now()

		# Update roloposconfig document
		filter_query = {"tenantId": tenant_id}
		update_query = {
			"$set": {
				"mappingConfig": existing_mapping_config
			}
		}

		result = roloposconfigsCol.update_one(filter_query, update_query)

		if result.matched_count == 0:
			raise HTTPException(status_code=404, detail="Tenant configuration not found")

		return MappingConfigResponse(
			success=True,
			message="Department group created successfully",
			data={"groupId": new_group_id, "group": new_group}
		)

	except HTTPException:
		raise
	except Exception as e:
		raise HTTPException(status_code=500, detail=f"Failed to create department group: {str(e)}")

@router.put("/update-department-group")
async def update_department_group(request: dict, token: str = Depends(authenticate)):
	"""
	Update an existing department group
	"""
	try:
		tenant_id = request.get("tenantId")
		group_id = request.get("groupId")
		group_name = request.get("groupName")
		group_description = request.get("groupDescription", "")
		department_ids = request.get("departmentIds", [])

		if not tenant_id or not group_id or not group_name:
			raise HTTPException(status_code=400, detail="tenantId, groupId, and groupName are required")

		# Get existing mapping config
		config = roloposconfigsCol.find_one({"tenantId": tenant_id})
		if not config:
			raise HTTPException(status_code=404, detail="Tenant configuration not found")

		existing_mapping_config = config.get("mappingConfig", {})
		existing_groups = existing_mapping_config.get("departmentGroups", [])

		# Find and update the group
		group_found = False
		for i, group in enumerate(existing_groups):
			if group.get("id") == group_id:
				existing_groups[i] = {
					"id": group_id,
					"name": group_name,
					"description": group_description,
					"departmentIds": department_ids,
					"isActive": True
				}
				group_found = True
				break

		if not group_found:
			raise HTTPException(status_code=404, detail="Department group not found")

		existing_mapping_config["departmentGroups"] = existing_groups
		existing_mapping_config["lastUpdated"] = datetime.now()

		# Update roloposconfig document
		filter_query = {"tenantId": tenant_id}
		update_query = {
			"$set": {
				"mappingConfig": existing_mapping_config
			}
		}

		result = roloposconfigsCol.update_one(filter_query, update_query)

		if result.matched_count == 0:
			raise HTTPException(status_code=404, detail="Tenant configuration not found")

		return MappingConfigResponse(
			success=True,
			message="Department group updated successfully",
			data={"groupId": group_id}
		)

	except HTTPException:
		raise
	except Exception as e:
		raise HTTPException(status_code=500, detail=f"Failed to update department group: {str(e)}")


@router.delete("/delete-department-group")
async def delete_department_group(request: dict, token: str = Depends(authenticate)):
	"""
	Delete a department group and all its associated mappings
	"""
	try:
		tenant_id = request.get("tenantId")
		group_id = request.get("groupId")

		if not tenant_id or not group_id:
			raise HTTPException(status_code=400, detail="tenantId and groupId are required")

		# Get existing mapping config
		config = roloposconfigsCol.find_one({"tenantId": tenant_id})
		if not config:
			raise HTTPException(status_code=404, detail="Tenant configuration not found")

		existing_mapping_config = config.get("mappingConfig", {})
		existing_groups = existing_mapping_config.get("departmentGroups", [])

		# Find and remove the group
		group_found = False
		group_name = ""
		for i, group in enumerate(existing_groups):
			if group.get("id") == group_id:
				group_name = group.get("name", "Unknown")
				existing_groups.pop(i)
				group_found = True
				break

		if not group_found:
			raise HTTPException(status_code=404, detail="Department group not found")

		# Remove group-category mappings
		if "departmentGroupCategoryMappings" in existing_mapping_config:
			existing_mapping_config["departmentGroupCategoryMappings"] = [
				mapping for mapping in existing_mapping_config["departmentGroupCategoryMappings"]
				if mapping.get("groupId") != group_id
			]

		# Remove group-workarea mappings
		if "departmentGroupWorkareaMappings" in existing_mapping_config:
			existing_mapping_config["departmentGroupWorkareaMappings"] = [
				mapping for mapping in existing_mapping_config["departmentGroupWorkareaMappings"]
				if mapping.get("groupId") != group_id
			]

		existing_mapping_config["departmentGroups"] = existing_groups
		existing_mapping_config["lastUpdated"] = datetime.now()

		# Update roloposconfig document
		filter_query = {"tenantId": tenant_id}
		update_query = {
			"$set": {
				"mappingConfig": existing_mapping_config
			}
		}

		result = roloposconfigsCol.update_one(filter_query, update_query)

		if result.matched_count == 0:
			raise HTTPException(status_code=404, detail="Tenant configuration not found")

		return MappingConfigResponse(
			success=True,
			message=f"Department group '{group_name}' and all its mappings deleted successfully"
		)

	except HTTPException:
		raise
	except Exception as e:
		raise HTTPException(status_code=500, detail=f"Failed to delete department group: {str(e)}")
