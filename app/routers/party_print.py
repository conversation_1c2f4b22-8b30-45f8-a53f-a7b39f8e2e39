from fastapi import <PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.database import mappingCol
import os
from bson import ObjectId
from fastapi import Query
from pydantic import BaseModel
from io import BytesIO
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.units import  mm, cm
from reportlab.lib.utils import ImageReader
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.utils import ImageReader
from io import BytesIO
import base64

router = APIRouter()
security = HTTPBearer()
async def authenticate(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if not credentials:
        raise HTTPException(status_code=401, detail="Not authenticated")
    expected_token = os.getenv("BEARER_TOKEN")
    if credentials.credentials != expected_token:
        raise HTTPException(status_code=401, detail="Invalid token")
    return credentials.credentials

class party(BaseModel):
    factory_name: str
    logo: str
    party_details: dict
    menu_table_headers: list
    menus_data: list
    supply_table_headers: list
    supply_data: list
    summary: dict
    type: str

    
def party_pdf(data : party):
    print('******************')
    buffer = BytesIO()
    pdf = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=14, leftMargin=14, topMargin=0, bottomMargin=15)
    elements = []
    styles = getSampleStyleSheet()
    styles.add(ParagraphStyle(name='CustomNormal', fontSize=9, leading=10))
    styles.add(ParagraphStyle(name='CustomBold', fontSize=12, leading=10, fontName='Helvetica-Bold'))
    styles.add(ParagraphStyle(name='LeftAlign', fontSize=8, leading=10, alignment=0))
    title = Paragraph(f"<b>{data.factory_name}</b>", styles['CustomBold'])
    styles.add(ParagraphStyle(
        name='titlesData',
        fontName='Helvetica-Bold', 
    ))

    # logo and name
    base = data.logo
    if base.startswith("data:image/png;base64,"):
        base = base.replace("data:image/png;base64,", "")
    logo_data = base64.b64decode(base)
    logo = Image(BytesIO(logo_data), width=50, height=50)

    header_table_data = [
        [Paragraph(data.factory_name, styles['Title']), logo]
    ]
    header_table = Table(header_table_data)
    header_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (0, 0), 'LEFT'),
        ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
        ('VALIGN', (0, 0), (1, 0), 'MIDDLE'),
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
    ]))
    elements.append(header_table)
    elements.append(Spacer(1, 28))

    # party Details
    party_details_data = []
    if data.party_details:
        # party_details_data = [
        #     [Paragraph(f"<b>{key}</b>", styles['CustomNormal']), Paragraph(f"{value.upper()}", styles['CustomNormal'])]
        #     for key, value in data.party_details.items()
        # ]
        
        # party_details_data = [
        #     [
        #         Paragraph(f"<b>{key}</b>", styles['CustomNormal']),
        #         Paragraph(f"{', '.join(value) if isinstance(value, list) else value.upper()}", styles['CustomNormal'])
        #     ]
        #     for key, value in data.party_details.items()
        # ]
        
        party_details_data = [
            [
                Paragraph(f"<b>{key}</b>", styles['CustomNormal']),
                Paragraph(
                    # {idx + 1}. 
                    "\n".join([f"{item['name']} - {item['time']}" for idx, item in enumerate(value)]) 
                    if isinstance(value, list) else (str(value).upper() if isinstance(value, str) else str(value)),
                    styles['CustomNormal']
                )
            ]
            for key, value in data.party_details.items()
        ]
 
    combined_data = Table([[Table(party_details_data, colWidths=[50*mm, None])]]) 
    combined_data.setStyle(TableStyle([
        ('ALIGN', (0, 0), (0, 0), 'LEFT'),
        ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
        ('VALIGN', (0, 0), (1, 0), 'TOP'),
        ('LEFTPADDING', (0, 0), (-1, -1), 0),
        ('RIGHTPADDING', (0, 0), (-1, -1), 0),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 0),
        ('TOPPADDING', (0, 0), (-1, -1), 0),
    ]))
    elements.append(combined_data)
    elements.append(Spacer(1, 28))

    # Extra Supplies
    # elements.append(Paragraph("<b>EXTRA SUPPLIES</b>", styles['titlesData']))
    extra_supply_data = [[Paragraph('EXTRA SUPPLIES', styles['titlesData'])] ]
    extraSupply = Table(extra_supply_data)
    extraSupply.setStyle(TableStyle([
        ('ALIGN', (0, 0), (0, 0), 'LEFT'),
        ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
        ('VALIGN', (0, 0), (1, 0), 'LEFT'),
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
    ]))
    elements.append(extraSupply)
    elements.append(Spacer(1, 8))
    supply_data = [[header.capitalize() for header in data.supply_table_headers]]
    for supply in data.supply_data:
        supply_data.append([supply["supplyName"], supply["quantity"], f"{supply['totalPrice']}"])

    # Adjust the column widths dynamically to fit inside the page
    supply_table = Table(supply_data, colWidths=[184, 184, 184], hAlign='LEFT')  # Adjust column widths if necessary
    supply_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
        ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.black)  # Optional: white text for header
    ]))
    elements.append(supply_table)
    elements.append(Spacer(1, 28))


    # Menus Items
    # elements.append(Paragraph("MENU ITEMS", styles['titlesData']))
    # elements.append(Spacer(1, 8))
    menuItem_Heading = [[Paragraph('MENU ITEMS', styles['titlesData'])] ]
    menuItemHeading = Table(menuItem_Heading)
    menuItemHeading.setStyle(TableStyle([
        ('ALIGN', (0, 0), (0, 0), 'LEFT'),
        ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
        ('VALIGN', (0, 0), (1, 0), 'LEFT'),
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
    ]))
    elements.append(menuItemHeading)
    elements.append(Spacer(1, 8))

    for menu in data.menus_data:
        # elements.append(Paragraph(menu["groupName"], styles['Heading4']))
        elements.append(Paragraph("<font size=8><b>{}</b></font>".format(menu["groupName"])))
        elements.append(Spacer(1, 5))

        menu_data = [[header.capitalize() for header in data.menu_table_headers]]
        for item in menu["items"]:
            menu_data.append([
                item["menuItemName"], item["servingSize"], item["quantity"],
                f"{item['costOfProduction']}", f"{item['overAllCostOfProduction']}",
                f"{item['sellingPrice']}", item["wastage"], item["return"], f"{item['totalPrice']}"
            ])
        
        # Adjust the column widths and ensure it fits inside the page
        menu_table = Table(menu_data, colWidths=[80, 50, 50, 70, 70, 70, 50, 50, 70])  # Adjust as needed
        menu_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 8),
        ]))
        elements.append(menu_table)
        elements.append(Spacer(1, 28))

    # Summary
    # elements.append(Paragraph("<font color='#808080'><b>COST DETAILS</b></font>", styles['Heading2']))
    # elements.append(Spacer(1, 8))
    summary_Heading = [[Paragraph('COST DETAILS', styles['titlesData'])] ]
    summaryData = Table(summary_Heading)
    summaryData.setStyle(TableStyle([
        ('ALIGN', (0, 0), (0, 0), 'LEFT'),
        ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
        ('VALIGN', (0, 0), (1, 0), 'LEFT'),
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
    ]))
    elements.append(summaryData)
    elements.append(Spacer(1, 8))
    summary = data.summary
    summary_data = []
    if summary:
        # summary_data = [
        #     [Paragraph(f"<b>{key}</b>", styles['CustomNormal']), Paragraph(f"{value.upper()}", styles['CustomNormal'])]
        #     for key, value in summary.items()
        # ]
        summary_data = [
            [Paragraph(f"<b>{key}</b>", styles['CustomNormal']), 
            Paragraph(f" {value.upper()} rs", styles['CustomNormal'])]
            for key, value in summary.items()
        ]

        # Create a table with 2 columns: recipe details on the left, logo on the right
    combined_data = Table([[Table(summary_data, colWidths=[50*mm, None])]]) 
    # Adjusting width to balance between text and logo , colWidths=[A4[0] - 4*cm, 3*cm], logo
    combined_data.setStyle(TableStyle([
        ('ALIGN', (0, 0), (0, 0), 'LEFT'),
        ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
        ('VALIGN', (0, 0), (1, 0), 'TOP'),
        ('LEFTPADDING', (0, 0), (-1, -1), 0),
        ('RIGHTPADDING', (0, 0), (-1, -1), 0),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 0),
        ('TOPPADDING', (0, 0), (-1, -1), 0),
    ]))
    elements.append(combined_data)
    
    # Build PDF and get base64 encoding
    pdf.build(elements)
    pdf_value = buffer.getvalue()
    buffer.close()
    pdf_base64 = base64.b64encode(pdf_value).decode('utf-8')
    return pdf_base64
        
    
@router.post("/generate-party-invoice")
def generate_party_invoice(data: party):
    pdf_base64 = party_pdf(data)
    return {"pdf_base64": pdf_base64}
