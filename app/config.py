from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    DATABASE_URL: str
    MONGO_INITDB_DATABASE: str

    class Config:
        env_file = './.env'
        extra = 'allow'


settings = Settings()

inventory_sheets = [
    'inventory master',
    'packagingmasters',
    'vendors'
]

user_sheets = [
    'Roles',
    'users',
    'branches'
]

recipe_sheets = [
    'menu master',
    'menu recipes',
    'Subrecipe Master',
    'Subrecipe Recipe',
    'servingsize conversion',
    'branches',
    'menu-to-workArea-mapping'
]
