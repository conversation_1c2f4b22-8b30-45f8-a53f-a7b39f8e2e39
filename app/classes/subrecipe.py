from app.utility.masterDataImport import masterDataImport
from app.database import branchesCol,productionsourcecol,servingsizerecipesCol,Stockvalues,restinvcol
import pandas as pd
from datetime import datetime
from app.utility.helper import errorValuesFilter,truncate_and_floor

class subRecipe():
	def __init__(self, tenant_id,masterdata_file,sessionId,modified_rows) :
		self.tenant_id = tenant_id
		self.masterdata_file = masterdata_file
		self.sessionId = sessionId
		self.modified_rows = modified_rows

		############################## DATA READ ##########################
		self.sub_recipe_master_data = masterDataImport(self.masterdata_file, 'Subrecipe Master',tenant_id,  readFull=False, sessionId=self.sessionId)
		self.sub_recipe_recipe_data = masterDataImport(self.masterdata_file, 'Subrecipe Recipe',tenant_id,  readFull=False, sessionId=self.sessionId)
		
		######################### PRE PROCESSING ##########################
		self.location_mapping = {}
		self.central_kitchen_mapping = {}
		self.workarea_mapping = {}
		for branch in branchesCol.find({"tenantId": self.tenant_id}):
			self.location_mapping[branch['abbreviatedRestaurantId']] = branch['restaurantIdOld']
			self.workarea_mapping[branch['restaurantIdOld']] = branch['workAreas']
			if branch['branchType'] == 'central kitchen':
				self.central_kitchen_mapping[branch['abbreviatedRestaurantId']] = branch['restaurantIdOld']

		################### TRIGGERING OTHER FUNCTIONS ###################
		self.stock_values_and_rest_inv()
		self.servingsize_recipes()
		self.production_source()


	def stock_values_and_rest_inv(self):
		for _index, row in self.sub_recipe_master_data.iterrows():
			status = 'discontinued' if row['Discontinued'].strip().lower() in ['y', 'yes'] else 'active'
			prepared_at = set([p.strip() for p in str(row['preparedAt']).split(',')])
			used_at_outlets = set([u.strip() for u in str(row['usedAtOutlet']).split(',')])
			locations = list(prepared_at.union(used_at_outlets))
			work_areas = [w.strip() for w in row['usedInWorkArea'].split(',')]
			unit_cost = row['rate'] if not pd.isnull(row['rate']) and row['rate'] != '' else 1.0
			portionWeight = truncate_and_floor((float(row['recovery'])/float(row['portion'])),2) if not pd.isnull(row['recovery']) and row['recovery'] != '' else 0.0
   
			if portionWeight != 0 :
				conversionCoefficient = 1
				if row['closingUOM'] != 'NOS' :
					conversionCoefficient = 1000
				unit_cost = truncate_and_floor(((float(row['rate']) / float(row['recovery'])) * conversionCoefficient),2)

			for location in locations:
				if location not in self.location_mapping:
					continue
				############### PROCESSING FOR STOCKVALUES ###############
				stock_dict = {}
				stock_dict = {
					'tenantId': self.tenant_id,
					'restaurantId': self.location_mapping[location],
					'category': str(row.get('category','N/A')).upper(),
					'subCategory': str(row.get('subCategory', 'N/A')).upper(),
					'itemCode': row['menuItemCode'].upper(),
					'itemName': row['menuItemName'].upper(),
					'inStock': 0,
					'workArea': {work_area: 0.0 for work_area in work_areas if work_area in self.workarea_mapping.get(self.location_mapping[location], [])},
					'ItemType': 'SubRecipe',
					'uom': row['closingUOM'],
					'menuGroup': {'name': str(row.get('subCategory', 'N/A')).upper()},
					'portionWeight': int(portionWeight), ## Converting mg to gm 
					'portion': float(row['portion']),
					'status' : status,
					'taxRate' : 0,
					'price' :  unit_cost,
					'withTaxPrice' : unit_cost,
					'modTs' : datetime.now()
				}
				prev_entry = None
				prev_entry = Stockvalues.find_one({'restaurantId': self.location_mapping[location], 'itemCode': row['menuItemCode'].upper()})
				if prev_entry:
					stock_dict['inStock'] = prev_entry.get('inStock', stock_dict['inStock'])
					stock_dict['workArea'] = {workArea: prev_entry['workArea'].get(workArea, 0) for workArea in stock_dict['workArea']}
					stock_dict['taxRate'] = prev_entry.get('taxRate', stock_dict['taxRate'])
					if row['menuItemCode'] not in self.modified_rows :
						stock_dict['withTaxPrice'] = prev_entry.get('withTaxPrice', stock_dict['withTaxPrice'])
						stock_dict['price'] = prev_entry.get('price', stock_dict['price'])
					Stockvalues.update_one({"_id": prev_entry['_id']},{"$set": stock_dict})
				else:
					stock_dict['createTs'] = datetime.now()
					Stockvalues.insert_one(stock_dict)

				################# PROCESSING FOR RESTINV #################
				rest_inv_dict ={}
				rest_inv_dict = {
					'tenantId': self.tenant_id,
					'restaurantId': self.location_mapping[location],
					'category': str(row.get('category', 'N/A')).upper(),
					'subCategory': str(row.get('subCategory', 'N/A')).upper(),
					'itemCode': row['menuItemCode'].upper(),
					'itemName': row['menuItemName'].upper(),
					'ItemType': 'SubRecipe',
					'uom': row['closingUOM'],
					'closingUom': row['closingUOM'],
					'purchaseUOM': '',
					'status': status,
					'statusHistory': [{'desc': 'created', 'field': 'None', 'val': 0, 'dateTs': datetime.now()}],
					'createTs': datetime.now(),
					'inKitchen': 0.0,
					'inStock': 0.0,
					'source': {'local': location in prepared_at, 'threshold': 10000.0},
					'optimumStock': 9999999.0,
					'consideredForClosing': True,
					'hasReceipe': True,
					'isSubReceipe': True,
					'menuGroup': {'name': str(row.get('subCategory', 'N/A')).upper()},
					'workArea': {work_area: 0.0 for work_area in work_areas if work_area in self.workarea_mapping.get(self.location_mapping[location], [])},
					'leadTime': 1,
					'stockCover': 1,
					'grnThreshold': {'extra': 10, 'deficit': 10},
					'weightInUse': row['weightInUse'] if not pd.isnull(row['weightInUse']) and row['weightInUse'] != '' else 0.0,
					'yield': row['yield'] if not pd.isnull(row['yield']) and row['yield'] != '' else 1.0,
					'rate': unit_cost,
					'portion': row['portion'],
					'portionWeight': int(portionWeight), ## Converting mg to gm 
					'modTs' : datetime.now()
				}
				prev_entry = None             
				prev_entry = restinvcol.find_one({'restaurantId': self.location_mapping[location], 'itemCode': row['menuItemCode'].upper()})
				if prev_entry:
					restinvcol.update_one({"_id": prev_entry['_id']},{"$set": rest_inv_dict})
				else:
					rest_inv_dict['createTs'] = datetime.now()
					restinvcol.insert_one(rest_inv_dict)

	def servingsize_recipes(self):
		batch_size_dict = {}
		sub_recipe_dict = {}
		srMaster = self.sub_recipe_master_data
		for _index, row in self.sub_recipe_recipe_data.iterrows():
			if row['Discontinued'].lower() in ['y', 'yes']:
				continue
			initial_weight = float(errorValuesFilter(row['Initialweight'],1.0))
			ingredient_yield = float(errorValuesFilter(row['yield'], 1.0))
			item_code = row['subRecipeCode'].upper()
			requiredSR = srMaster[srMaster["menuItemCode"].str.upper() == row['subRecipeCode'].upper()]
			if item_code not in sub_recipe_dict:
				sub_recipe_dict[item_code] = {
					'tenantId' : self.tenant_id,
					'menuItemCode' : item_code,
					'menuItemName' : row['subRecipeName'].upper(),
					'isSubRecipe' : True,
					'servingSizeRatio' : 1.0,
					'yield' : float(requiredSR.iloc[0]["yield"]),
					'servingSize' : '(F)',
					'Ingredients' : [{
						"IngredientCode" : row['ingredientCode'].upper(),
						"IngredientName" : row['ingredientName'].upper(),
						"initialWeight" : initial_weight,
						"WeightInUse" : initial_weight * ingredient_yield,
						"ingredientUom" : row['UOM'],
						"ingredientYield" : ingredient_yield,
						'rate' : errorValuesFilter(row['rate'], 0.0),
						'finalRate' : errorValuesFilter( row['finalRate'], 0.0),
					}]
				}
				batch_size_dict[item_code] = {}
				batch_size_dict[item_code]['initialWeight'] = initial_weight
				batch_size_dict[item_code]['WeightInUse'] = initial_weight * ingredient_yield
			else:
				batch_size_dict[item_code]['initialWeight'] += initial_weight
				batch_size_dict[item_code]['WeightInUse'] += (initial_weight * ingredient_yield)
				sub_recipe_dict[item_code]['Ingredients'].append({
					"IngredientCode" : row['ingredientCode'].upper(),
					"IngredientName" : row['ingredientName'].upper(),
					"initialWeight" : initial_weight,
					"WeightInUse" : initial_weight * ingredient_yield,
					"ingredientUom" : row['UOM'],
					"ingredientYield" : ingredient_yield,
					'rate' : errorValuesFilter(row['rate'], 0.0),
					'finalRate' : errorValuesFilter( row['finalRate'], 0.0),
				})
		for key in sub_recipe_dict.keys():
			sub_recipe_dict[key]['batchSize'] = batch_size_dict[key]['initialWeight']/1000
			for item in sub_recipe_dict[key]['Ingredients']:
				item['initialWeight'] = (item['initialWeight']/batch_size_dict[key]['initialWeight'])*1000
				item['WeightInUse'] = (item['WeightInUse']/batch_size_dict[key]['WeightInUse'])*1000

		for menu_item_code in sub_recipe_dict.keys():
			current_entry = sub_recipe_dict[menu_item_code]
			current_entry['modTs'] = datetime.now()
			prev_entry = None
			prev_entry = servingsizerecipesCol.find_one({'tenantId': self.tenant_id, 'menuItemCode': menu_item_code})
			if prev_entry:
				servingsizerecipesCol.update_one({"_id": prev_entry['_id']},{"$set": current_entry})
			else:
				current_entry['createTs'] = datetime.now()
				servingsizerecipesCol.insert_one(current_entry)

	def production_source(self):
		for _index, row in self.sub_recipe_master_data.iterrows():
			restaurants = list(set([p.strip() for p in str(row['preparedAt']).split(',')]))
			for restaurant in restaurants:
				if restaurant in self.central_kitchen_mapping:
					current_entry = {}
					current_entry = {
						'itemCode' : row['menuItemCode'].upper(),
						'itemName' : row['menuItemName'].upper(),
						'tenantId' : self.tenant_id,
						'restaurantId' : self.central_kitchen_mapping[restaurant],
						'priority' : 1,
						'flag' : True,
						'percentage' : 100,
						'threshold' : 10000,
						'modTs' : datetime.now()
					}
					prev_entry = None
					prev_entry = productionsourcecol.find_one({'restaurantId': restaurant, 'itemCode': row['menuItemCode'].upper()})
					if prev_entry:
						current_entry['createTs'] = prev_entry.get('createTs', datetime.now())
						productionsourcecol.update_one({"_id": prev_entry['_id']},{"$set": current_entry})
					else:
						current_entry['createTs'] = datetime.now()
						productionsourcecol.insert_one(current_entry)

