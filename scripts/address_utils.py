"""Utility functions for address processing and state extraction."""
import re
import logging
from typing import Optional

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

STATE_MAPPING = {
    'ANDHRA PRADESH': 'Andhra Pradesh',
    'ANDHRA': 'Andhra Pradesh',
    'AP': 'Andhra Pradesh',
    'ARUNACHAL PRADESH': 'Arunachal Pradesh',
    'ASSAM': 'Assam',
    'BIHAR': 'Bihar',
    'CHHATTISGARH': 'Chhattisgarh',
    'CHATTISGARH': 'Chhattisgarh',
    'GOA': 'Goa',
    'GUJARAT': 'Gujarat',
    'GJ': 'Gujarat',
    'HARYANA': 'Haryana',
    'HIMACHAL PRADESH': 'Himachal Pradesh',
    'HP': 'Himachal Pradesh',
    'JHARKHAND': 'Jharkhand',
    'KARNATAKA': 'Karnataka',
    'KA': 'Karnataka',
    'KERALA': 'Kerala',
    'KL': 'Kerala',
    'MADHYA PRADESH': 'Madhya Pradesh',
    'MP': 'Madhya Pradesh',
    'MAHARASHTRA': 'Maharashtra',
    'MH': 'Maharashtra',
    'MANIPUR': 'Manipur',
    'MEGHALAYA': 'Meghalaya',
    'MIZORAM': 'Mizoram',
    'NAGALAND': 'Nagaland',
    'ODISHA': 'Odisha',
    'ORISSA': 'Odisha',
    'PUNJAB': 'Punjab',
    'PB': 'Punjab',
    'RAJASTHAN': 'Rajasthan',
    'RJ': 'Rajasthan',
    'SIKKIM': 'Sikkim',
    'TAMIL NADU': 'Tamil Nadu',
    'TAMILNADU': 'Tamil Nadu',
    'TN': 'Tamil Nadu',
    'TELANGANA': 'Telangana',
    'TS': 'Telangana',
    'TG': 'Telangana',
    'TRIPURA': 'Tripura',
    'UTTAR PRADESH': 'Uttar Pradesh',
    'UP': 'Uttar Pradesh',
    'UTTARAKHAND': 'Uttarakhand',
    'UK': 'Uttarakhand',
    'WEST BENGAL': 'West Bengal',
    'WB': 'West Bengal',
    'DELHI': 'Delhi',
    'DL': 'Delhi',
    'NCT': 'Delhi',
    'NCT OF DELHI': 'Delhi',
    'NEW DELHI': 'Delhi',
    'JAMMU AND KASHMIR': 'Jammu and Kashmir',
    'J&K': 'Jammu and Kashmir',
    'JAMMU & KASHMIR': 'Jammu and Kashmir',
    'LADAKH': 'Ladakh',
    'PUDUCHERRY': 'Puducherry',
    'PONDICHERRY': 'Puducherry',
    'CHANDIGARH': 'Chandigarh',
    'CH': 'Chandigarh',
    'ANDAMAN AND NICOBAR ISLANDS': 'Andaman and Nicobar Islands',
    'ANDAMAN & NICOBAR': 'Andaman and Nicobar Islands',
    'A&N ISLANDS': 'Andaman and Nicobar Islands',
    'DADRA AND NAGAR HAVELI AND DAMAN AND DIU': 'Dadra and Nagar Haveli and Daman and Diu',
    'DADRA & NAGAR HAVELI & DAMAN & DIU': 'Dadra and Nagar Haveli and Daman and Diu',
    'DAMAN AND DIU': 'Dadra and Nagar Haveli and Daman and Diu',
    'DAMAN & DIU': 'Dadra and Nagar Haveli and Daman and Diu',
    'DADRA AND NAGAR HAVELI': 'Dadra and Nagar Haveli and Daman and Diu',
    'DADRA & NAGAR HAVELI': 'Dadra and Nagar Haveli and Daman and Diu',
    'LAKSHADWEEP': 'Lakshadweep',
}

def extract_state_from_address(address: str, use_geopy: bool = False) -> Optional[str]:
    """Extract state information from an address string."""
    if not address or not isinstance(address, str):
        return None

    # Try pincode extraction first
    pincode_match = re.search(r'\b(\d{6})\b', address)
    if pincode_match:
        pincode = pincode_match.group(1)
        state = get_state_from_pincode(pincode)
        if state:
            return state

    address_upper = address.upper()

    # Check comma-separated parts
    for part in address_upper.split(','):
        part = part.strip()
        for state_variant, standard_name in STATE_MAPPING.items():
            if re.search(r'\b' + re.escape(state_variant) + r'\b', part):
                return standard_name

    # Check space-separated parts
    for part in re.split(r'\s+', address_upper):
        if part in STATE_MAPPING:
            return STATE_MAPPING[part]

    # Check full address for state names
    for state_variant, standard_name in STATE_MAPPING.items():
        if re.search(r'\b' + re.escape(state_variant) + r'\b', address_upper):
            return standard_name

    # City to state mapping
    common_cities = {
        'MUMBAI': 'Maharashtra',
        'DELHI': 'Delhi',
        'BANGALORE': 'Karnataka',
        'BENGALURU': 'Karnataka',
        'HYDERABAD': 'Telangana',
        'AHMEDABAD': 'Gujarat',
        'CHENNAI': 'Tamil Nadu',
        'KOLKATA': 'West Bengal',
        'SURAT': 'Gujarat',
        'PUNE': 'Maharashtra',
        'JAIPUR': 'Rajasthan',
        'LUCKNOW': 'Uttar Pradesh',
        'KANPUR': 'Uttar Pradesh',
        'NAGPUR': 'Maharashtra',
        'INDORE': 'Madhya Pradesh',
        'THANE': 'Maharashtra',
        'BHOPAL': 'Madhya Pradesh',
        'VISAKHAPATNAM': 'Andhra Pradesh',
        'PATNA': 'Bihar',
        'VADODARA': 'Gujarat',
        'GHAZIABAD': 'Uttar Pradesh',
        'LUDHIANA': 'Punjab',
        'AGRA': 'Uttar Pradesh',
        'NASHIK': 'Maharashtra',
        'FARIDABAD': 'Haryana',
        'MEERUT': 'Uttar Pradesh',
        'RAJKOT': 'Gujarat',
        'KALYAN': 'Maharashtra',
        'VASAI': 'Maharashtra',
        'VARANASI': 'Uttar Pradesh',
        'SRINAGAR': 'Jammu and Kashmir',
        'AURANGABAD': 'Maharashtra',
        'DHANBAD': 'Jharkhand',
        'AMRITSAR': 'Punjab',
        'NAVI MUMBAI': 'Maharashtra',
        'ALLAHABAD': 'Uttar Pradesh',
        'PRAYAGRAJ': 'Uttar Pradesh',
        'RANCHI': 'Jharkhand',
        'HOWRAH': 'West Bengal',
        'COIMBATORE': 'Tamil Nadu',
        'JABALPUR': 'Madhya Pradesh',
        'GWALIOR': 'Madhya Pradesh',
        'VIJAYAWADA': 'Andhra Pradesh',
        'JODHPUR': 'Rajasthan',
        'MADURAI': 'Tamil Nadu',
        'RAIPUR': 'Chhattisgarh',
        'KOCHI': 'Kerala',
        'COCHIN': 'Kerala',
        'CHANDIGARH': 'Chandigarh',
        'GUWAHATI': 'Assam',
        'BHUBANESWAR': 'Odisha',
        'DEHRADUN': 'Uttarakhand',
        'PONDICHERRY': 'Puducherry',
        'PUDUCHERRY': 'Puducherry',
        'SHIMLA': 'Himachal Pradesh',
        'PANAJI': 'Goa',
        'GANDHINAGAR': 'Gujarat',
        'SILVASSA': 'Dadra and Nagar Haveli and Daman and Diu',
        'DAMAN': 'Dadra and Nagar Haveli and Daman and Diu',
        'KAVARATTI': 'Lakshadweep',
        'PORT BLAIR': 'Andaman and Nicobar Islands',
        'ITANAGAR': 'Arunachal Pradesh',
        'DISPUR': 'Assam',
        'GANGTOK': 'Sikkim',
        'KOHIMA': 'Nagaland',
        'IMPHAL': 'Manipur',
        'AIZAWL': 'Mizoram',
        'AGARTALA': 'Tripura',
        'SHILLONG': 'Meghalaya',
    }

    # Check for city names
    for city, state in common_cities.items():
        if re.search(r'\b' + re.escape(city) + r'\b', address_upper):
            return state

    # Use geopy if requested
    if use_geopy:
        try:
            state = extract_state_using_geopy(address)
            if state:
                return state
        except Exception:
            pass

    return None

def extract_state_using_geopy(address: str) -> Optional[str]:
    """Extract state information from an address using geopy."""
    if len(address) < 10:
        return None

    try:
        from geopy.geocoders import Nominatim
        import socket

        geolocator = Nominatim(
            user_agent="digitoryjobsv4_state_extractor",
            timeout=5
        )

        if not any(state_name in address.upper() for state_name in STATE_MAPPING):
            if not re.search(r'\b\d{6}\b', address):
                return None

        socket.setdefaulttimeout(5)
        location = geolocator.geocode(address, addressdetails=True)

        if location and location.raw.get('address'):
            address_components = location.raw['address']
            state = (
                address_components.get('state') or
                address_components.get('territory') or
                address_components.get('state_district')
            )

            if state and state.upper() in STATE_MAPPING:
                return STATE_MAPPING[state.upper()]
            elif state and state in set(STATE_MAPPING.values()):
                return state
    except ImportError:
        return None
    except (socket.timeout, socket.error):
        return None
    except Exception as e:
        logger.warning(f"Geopy error: {str(e)}")
        return None

    return None

def get_state_from_pincode(pincode: str) -> Optional[str]:
    """Get state name based on the first two digits of a pincode."""
    if not pincode or len(pincode) != 6 or not pincode.isdigit():
        return None

    # Special case ranges
    if pincode.startswith('603'):
        return 'Puducherry'
    if pincode.startswith('396'):
        return 'Dadra and Nagar Haveli and Daman and Diu'
    if pincode.startswith('737'):
        return 'Sikkim'
    if pincode.startswith('60') and not pincode.startswith('603'):
        return 'Tamil Nadu'

    # First two digits of pincode indicate state
    prefix = pincode[:2]

    # Valid prefix check
    if not prefix.startswith(('1', '2', '3', '4', '5', '6', '7', '8', '9')):
        return None

    pincode_state_map = {
        '11': 'Delhi',
        '12': 'Haryana',
        '13': 'Punjab',
        '14': 'Punjab',
        '15': 'Punjab',
        '16': 'Punjab',
        '17': 'Himachal Pradesh',
        '18': 'Jammu and Kashmir',
        '19': 'Jammu and Kashmir',
        '20': 'Uttar Pradesh',
        '21': 'Uttar Pradesh',
        '22': 'Uttar Pradesh',
        '23': 'Uttar Pradesh',
        '24': 'Uttar Pradesh',
        '25': 'Uttar Pradesh',
        '26': 'Uttar Pradesh',
        '27': 'Uttar Pradesh',
        '28': 'Uttar Pradesh',
        '30': 'Rajasthan',
        '31': 'Rajasthan',
        '32': 'Rajasthan',
        '33': 'Rajasthan',
        '34': 'Rajasthan',
        '36': 'Gujarat',
        '37': 'Gujarat',
        '38': 'Gujarat',
        '39': 'Gujarat',
        '40': 'Maharashtra',
        '41': 'Maharashtra',
        '42': 'Maharashtra',
        '43': 'Maharashtra',
        '44': 'Maharashtra',
        '45': 'Maharashtra',
        '46': 'Maharashtra',
        '47': 'Maharashtra',
        '48': 'Maharashtra',
        '49': 'Maharashtra',
        '50': 'Telangana',
        '51': 'Telangana',
        '52': 'Andhra Pradesh',
        '53': 'Andhra Pradesh',
        '56': 'Karnataka',
        '57': 'Karnataka',
        '58': 'Karnataka',
        '59': 'Karnataka',
        '60': 'Tamil Nadu',
        '61': 'Tamil Nadu',
        '62': 'Tamil Nadu',
        '63': 'Tamil Nadu',
        '64': 'Tamil Nadu',
        '67': 'Kerala',
        '68': 'Kerala',
        '69': 'Kerala',
        '70': 'West Bengal',
        '71': 'West Bengal',
        '72': 'West Bengal',
        '73': 'West Bengal',
        '74': 'West Bengal',
        '75': 'Odisha',
        '76': 'Odisha',
        '77': 'Odisha',
        '78': 'Assam',
        '79': 'Assam',
        '80': 'Chhattisgarh',
        '81': 'Chhattisgarh',
        '82': 'Jharkhand',
        '83': 'Jharkhand',
        '84': 'Bihar',
        '85': 'Bihar',
        '90': 'Uttarakhand',
        '91': 'Uttarakhand',
        '93': 'Sikkim',
        '94': 'Andaman and Nicobar Islands',
        '16': 'Chandigarh',
        '19': 'Ladakh',
    }

    return pincode_state_map.get(prefix)
