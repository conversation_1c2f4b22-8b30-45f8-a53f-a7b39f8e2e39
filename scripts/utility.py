def get_vendors(tenant_ids):
    pipeline =  [
        {"$match": {
            "servingTenantId": {"$in": tenant_ids},
            "tenantName": {"$nin": [None, ""]},
            "uType": "vendor"
        }},
        {"$group": {
            "_id": "$tenantName",
            "gstNos": {"$push": {"$cond": {
                "if": {"$and": [
                    {"$ne": ["$gstNo", None]},
                    {"$ne": ["$gstNo", ""]},
                    {"$ne": ["$gstNo", "NO_GST"]},
                    {"$ne": ["$gstNo", "xxxxxxxxxx"]},
                    {"$regexMatch": {"input": "$gstNo", "regex": "^[0-9A-Z]{15}$"}}
                ]},
                "then": "$gstNo",
                "else": None
            }}},
            "servingTenantIds": {"$addToSet": "$servingTenantId"},
            "categories": {"$push": {"$concatArrays": [
                {"$ifNull": [{"$cond": {"if": {"$isArray": "$invCategories.invCategoryName"}, "then": "$invCategories.invCategoryName", "else": []}}, []]},
                {"$ifNull": [{"$cond": {"if": {"$isArray": "$inventoryCategories.invCategoryName"}, "then": "$inventoryCategories.invCategoryName", "else": []}}, []]}
            ]}},
            "addresses": {"$push": {"$cond": {
                "if": {"$and": [
                    {"$ne": ["$address", None]},
                    {"$ne": ["$address", ""]},
                    {"$not": {"$regexMatch": {"input": "$address", "regex": "^(xxx|xxxxx|xxxxxxxxxx|xxxxxxxxxxxx|xyz|-389029|0)$"}}}
                ]},
                "then": {"k": "$servingTenantId", "v": "$address"},
                "else": None
            }}}
        }},
        {"$project": {
            "vendorName": "$_id",
            "validGstNos": {"$filter": {
                "input": "$gstNos", 
                "as": "gst",
                "cond": {"$ne": ["$$gst", None]}
            }},
            "sourcesCount": {"$size": "$servingTenantIds"},
            "servingTenantIds": 1,
            "categories": {"$reduce": {
                "input": "$categories", 
                "initialValue": [],
                "in": {"$concatArrays": ["$$value", "$$this"]}
            }},
            "addresses": {"$arrayToObject": {"$filter": {
                "input": "$addresses", 
                "as": "addr",
                "cond": {"$ne": ["$$addr", None]}
            }}}
        }},
        {"$addFields": {
            "gstCounts": {
                "$reduce": {
                    "input": "$validGstNos",
                    "initialValue": [],
                    "in": {
                        "$concatArrays": [
                            "$$value",
                            [{"k": "$$this", "v": {
                                "$add": [
                                    {"$size": {
                                        "$filter": {
                                            "input": "$validGstNos",
                                            "as": "g",
                                            "cond": {"$eq": ["$$g", "$$this"]}
                                        }
                                    }},
                                    0
                                ]
                            }}]
                        ]
                    }
                }
            }
        }},
        {"$addFields": {
            "uniqueGstCounts": {"$arrayToObject": {"$setUnion": "$gstCounts"}},
            "maxGstCount": {"$max": {"$map": {
                "input": "$gstCounts",
                "as": "gc",
                "in": "$$gc.v"
            }}}
        }},
        {"$addFields": {
            "majorityGsts": {
                "$filter": {
                    "input": {"$objectToArray": "$uniqueGstCounts"},
                    "as": "gstCount",
                    "cond": {"$eq": ["$$gstCount.v", "$maxGstCount"]}
                }
            }
        }},
        {"$addFields": {
            "gstNo": {"$cond": {
                "if": {"$gt": [{"$size": "$majorityGsts"}, 0]},
                "then": {"$arrayElemAt": ["$majorityGsts.k", 0]},
                "else": None
            }}
        }},
        {"$unwind": "$categories"},
        {"$group": {
            "_id": {"vendorName": "$vendorName", "category": "$categories"},
            "count": {"$sum": 1},
            "gstNo": {"$first": "$gstNo"},
            "servingTenantIds": {"$first": "$servingTenantIds"},
            "sourcesCount": {"$first": "$sourcesCount"},
            "addresses": {"$first": "$addresses"}
        }},
        {"$group": {
            "_id": "$_id.vendorName",
            "gstNo": {"$first": "$gstNo"},
            "servingTenantIds": {"$first": "$servingTenantIds"},
            "sourcesCount": {"$first": "$sourcesCount"},
            "topCategories": {"$push": {"k": "$_id.category", "v": "$count"}},
            "addresses": {"$first": "$addresses"}
        }},
        {"$project": {
            "vendorName": {"$toUpper": "$_id"},
            "gstNo": 1,
            "sourcesCount": 1,
            "servingTenantIds": 1,
            "topCategories": {"$arrayToObject": "$topCategories"},
            "addresses": 1
        }},
        {"$sort": {        
            "sourcesCount": -1, 
            "gstNo": -1
        }}
    ]
    return pipeline
    