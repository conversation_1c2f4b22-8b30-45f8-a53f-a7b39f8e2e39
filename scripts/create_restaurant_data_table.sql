-- Create restaurant_data table
CREATE TABLE IF NOT EXISTS restaurant_data (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(255) NOT NULL,
    data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries by tenant_id
CREATE INDEX IF NOT EXISTS idx_restaurant_tenant_id ON restaurant_data (tenant_id);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update the updated_at column
DROP TRIGGER IF EXISTS update_restaurant_data_updated_at ON restaurant_data;
CREATE TRIGGER update_restaurant_data_updated_at
BEFORE UPDATE ON restaurant_data
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
